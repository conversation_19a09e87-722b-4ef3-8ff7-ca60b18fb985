import { type Role, UserRole } from './types';

/**
 * Role descriptions for tooltips and displays
 */
export const roleDescriptions: Record<string, string> = {
  [UserRole.STANDARD]: 'Basic user access to the platform.',
  [UserRole.ADMIN]: 'Full administrative rights, including user and role management.',
  [UserRole.SALES_REP]: 'Can manage sales opportunities and customer records.',
  [UserRole.SALES_MANAGER]: 'Manages sales reps and oversees sales operations.',
  [UserRole.REGIONAL_LEAD]: 'Leads sales for a geographic region.',
  [UserRole.PRICE_DESK_ANALYST]: 'Handles pricing desk requests and approvals.',
  [UserRole.SERVICE_DESK_ANALYST]: 'Handles service desk requests and tickets.',
  [UserRole.DSA]: 'Direct Sales Associate responsibilities.',
  [UserRole.RFP_BID_DESK]: 'Manages RFP and bid desk submissions.'
};

/**
 * Available roles in the application
 */
export const availableRoles: Role[] = [
  { 
    id: '1', 
    value: UserRole.STANDARD, 
    label: 'Standard', 
    description: roleDescriptions[UserRole.STANDARD],
    users: 25
  },
  { 
    id: '2', 
    value: UserRole.ADMIN, 
    label: 'Admin', 
    description: roleDescriptions[UserRole.ADMIN],
    users: 5
  },
  { 
    id: '3', 
    value: UserRole.SALES_REP, 
    label: 'Sales Rep', 
    description: roleDescriptions[UserRole.SALES_REP],
    users: 15
  },
  { 
    id: '4', 
    value: UserRole.SALES_MANAGER, 
    label: 'Sales Manager', 
    description: roleDescriptions[UserRole.SALES_MANAGER],
    users: 8
  },
  { 
    id: '5', 
    value: UserRole.REGIONAL_LEAD, 
    label: 'Regional Lead', 
    description: roleDescriptions[UserRole.REGIONAL_LEAD],
    users: 4
  },
  { 
    id: '6', 
    value: UserRole.PRICE_DESK_ANALYST, 
    label: 'Price Desk Analyst', 
    description: roleDescriptions[UserRole.PRICE_DESK_ANALYST],
    users: 6
  },
  { 
    id: '7', 
    value: UserRole.SERVICE_DESK_ANALYST, 
    label: 'Service Desk Analyst', 
    description: roleDescriptions[UserRole.SERVICE_DESK_ANALYST],
    users: 10
  },
  { 
    id: '8', 
    value: UserRole.DSA, 
    label: 'DSA', 
    description: roleDescriptions[UserRole.DSA],
    users: 12
  },
  { 
    id: '9', 
    value: UserRole.RFP_BID_DESK, 
    label: 'RFP Bid Desk', 
    description: roleDescriptions[UserRole.RFP_BID_DESK],
    users: 3
  }
];

/**
 * Get role by value
 * @param value Role value
 * @returns Role object or undefined if not found
 */
export const getRoleByValue = (value: string): Role | undefined => {
  return availableRoles.find(role => role.value === value);
};

/**
 * Get role label by value
 * @param value Role value
 * @returns Role label or value if not found
 */
export const getRoleLabelByValue = (value: string): string => {
  const role = getRoleByValue(value);
  return role ? role.label : value;
};