/**
 * @file Single source of truth for determining which role is allowed
 * to perform specific actions. Each action should have its own method
 * outlined in this file with its corresponding access role assignment.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { Channel_Action, CN_Action, UserRole } from '@/lib/common/types';
import { CanonAuth } from '@/lib/canonAuth';
import { useInternalUserStore } from '@/stores/InternalUserStore';
	const userInternalStore = useInternalUserStore();

/**
 * ----
 * Main
 * ----
*/

// Defines application actions and their allowed roles.
const actions =
{
    // ----------
    // User Login
    // ----------
    [Channel_Action.SALES_OPERATIONS] : () =>
    [
        'User.Standard',
        'User.Admin'
    ],

    // -------------------
    // User Profile Update
    // -------------------
    [CN_Action.PA_USER_UPDATE] : () =>
    [
        'User.Standard',
        'User.Admin'
    ],

    // ------------------------------------
    // User Troubleshooting - Non-Sensitive
    // ------------------------------------
    [CN_Action.TROUBLESHOOT] : () =>
    [
        'User.Admin'
    ],

                  // ------------------------------------
    // User with Admin role
    // ------------------------------------
    [CN_Action.ADMIN] : () =>
        [
            'User.Admin'
        ],
};

/**
 * ----
 * Export
 * ----
*/

/**
 * Checks if the currently logged in user has at least one of the required roles
 * defined for the given action.
 *
 * @param {string} [actionId] Unique identifier for each action.
 * 
 * @return {boolean} Value indicating if permission is granted or not.
 */
export default ( actionId : CN_Action ) : boolean =>
{
    try
    {
        // Get the action's required roles.
        const roles : string[] = actions[ actionId ]();

        if ( roles && roles.length > 0 )
        {
            userInternalStore.
            // Check roles.
            return CanonAuth.hasAnyRoles( roles );
        }

        // There is no action or the action has no roles defined.
        return false;
        
    }
    catch
    {
        // Invalid action ID was passed.
        return false;
    }
};