<script setup lang="ts">
import { ref, reactive, defineProps, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import ServiceHeaderInfo from '@/components/service/ServiceHeaderInfo.vue';
import ServiceTonerPack from '@/components/service/ServiceTonerPack.vue';
import ServiceApprovalsLease from '@/components/service/ServiceApprovalsLease.vue';
import ServiceProductDetails from '@/components/service/ServiceProductDetails.vue';
import ServiceAccessoryList from '@/components/service/ServiceAccessoryList.vue';
import ServiceCompetitiveInfo from '@/components/service/ServiceCompetitiveInfo.vue';
import ServiceCurrentEquipment from '@/components/service/ServiceCurrentEquipment.vue';
import ServiceBusinessCase from '@/components/service/ServiceBusinessCase.vue';
// Import other service components here as they are created

const { t } = useI18n();

const props = defineProps({
  id: {
    type: String,
    required: false, // As the ID is optional in the route
    default: null,
  },
});

// Initialize formData based on the UI plan
const formData = reactive({
  service_request_form: {
    request_date: null,
    sales_representative_name: '',
    sales_manager_name: '',
    customer_business_name: '',
    customer_legal_name: '',
    address1: '',
    address2_3: '',
    city_province_postal: '',
  },
  toner_and_service_value_pack: {
    toner_in_out: null, // Default to null for placeholder
    service_value_pack: null, // Default to null for placeholder
  },
  approvals_details: {
    selected_region: null, // Changed from 'region' object
    dsd_territory: false,
    dealer_territory: false,
    dealer_accepted_service_rates: null, // 'Yes' or 'No'
    // coterm: null, // Removed
    purchase_lease_type: 'Lease', // Default
    length_of_lease_months: null,
    msrp_details: { percentage: null, amount: null },
  },
  product_service_details: {
    models_overview: [], // Array of product objects, managed by ServiceProductDetails
    accessories_included: [], // For the separate accessories table later
  },
  competitive_current_usage_info: {
    details: '',
  },
  current_equipment_details: {
    equipment_list: '',
  },
  service_business_case: {
    justification: 'Entera is mainly a Sharp customer with 3 Sharp units on the floor. They are currently paying a higher CPC for their Sharp units. They have agreed to move to Canon if we can provide them with a competitive CPC. The customer is also looking for a reliable service provider and Canon has a good reputation in the market. We are requesting a 10% discount on the service rates to win this deal. Potential penalties for not meeting service level agreements are 5% of the monthly service charge.', // Default text as per UI plan
  },
});

const handleFormSubmit = () => {
  console.log('Submitting Service Request Form:', formData);
  // API submission logic will go here
};

const cancelForm = () => {
  // Logic to navigate away or reset form
  console.log('Form cancelled');
};

onMounted(() => {
  if (props.id) {
    console.log('ServiceRequestFormPage mounted with ID:', props.id);
    // TODO: Fetch existing service request data using props.id
    // and populate formData. For example:
    // fetchServiceRequestData(props.id).then(data => {
    //   Object.assign(formData, data);
    // });
  } else {
    console.log('ServiceRequestFormPage mounted for new request (no ID).');
  }
});

</script>

<template>
  <v-container fluid>
    <v-form @submit.prevent="handleFormSubmit">
      <v-row>
        <v-col cols="12">
          <h1>Service Request Form</h1> <!-- Placeholder Title -->
        </v-col>
      </v-row>

      <!-- Section 1: Header & Basic Request Information -->
      <ServiceHeaderInfo v-model="formData.service_request_form" />

      <!-- Section 2: Toner and Service Value Pack -->
      <ServiceTonerPack v-model="formData.toner_and_service_value_pack" />

      <!-- Section 3: Approvals and Lease Details -->
      <ServiceApprovalsLease v-model="formData.approvals_details" />

      <!-- Section 4: Product/Service Details -->
      <ServiceProductDetails 
        v-model="formData.product_service_details.models_overview"
        :selected-service-value-pack="formData.toner_and_service_value_pack.service_value_pack"
      />

      <!-- Section 4.1: Accessories Included in Deal (Part of Product/Service Details) -->
      <ServiceAccessoryList v-model="formData.product_service_details.accessories_included" />

      <!-- Section 5: Competitive & Current Usage Information -->
      <ServiceCompetitiveInfo v-model="formData.competitive_current_usage_info.details" />

      <!-- Section 6: Current Equipment Details -->
      <ServiceCurrentEquipment v-model="formData.current_equipment_details.equipment_list" />

      <!-- Section 7: Service Business Case -->
      <ServiceBusinessCase v-model="formData.service_business_case.justification" />


      <v-row>
        <v-col cols="12" class="d-flex justify-end mt-4">
          <v-btn color="error" variant="outlined" @click="cancelForm" class="mr-2">
            Cancel
          </v-btn>
          <v-btn color="primary" type="submit">
            Submit
          </v-btn>
        </v-col>
      </v-row>
    </v-form>
  </v-container>
</template>

<style scoped>
/* Add any page-specific styles here */
</style>
