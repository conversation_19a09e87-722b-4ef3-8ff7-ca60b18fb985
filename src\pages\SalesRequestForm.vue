<script setup lang="ts">
/**
 * @file New Sales Request form page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppStore } from '@/stores/AppStore';
import { useRouter, useRoute } from 'vue-router';
import { saveSalesRequest, getSalesRequest } from '@/services/salesRequestService';

// Import tab components
import CustomerDetailsTab from '@/components/sales/CustomerDetailsTab.vue';
import PaymentDetailsTab from '@/components/sales/PaymentDetailsTab.vue';
import EquipmentsTab from '@/components/sales/EquipmentsTab.vue';
import WorksheetsTab from '@/components/sales/WorksheetsTab.vue';
import PreviewTab from '@/components/sales/PreviewTab.vue';
import { useSnackbarStore } from '@/stores/SnackbarStore';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();
const router = useRouter();
const route = useRoute();

// Add language support
const { t } = useI18n();

// Active tab tracking
const activeTab = ref(0);
const snackbarStore = useSnackbarStore();
const requestId = ref<number | null>(null);

// References to tab components for validation
const customerDetailsTabRef = ref<InstanceType<typeof CustomerDetailsTab> | null>(null);
const paymentDetailsTabRef = ref<InstanceType<typeof PaymentDetailsTab> | null>(null);
const equipmentsTabRef = ref<InstanceType<typeof EquipmentsTab> | null>(null);
const worksheetsTabRef = ref<InstanceType<typeof WorksheetsTab> | null>(null);
const previewTabRef = ref<InstanceType<typeof PreviewTab> | null>(null);

// Create reactive computed properties for the preview data
const previewCustomerDetails = computed(() => customerDetailsTabRef.value?.getFormData());
const previewPaymentDetails = computed(() => paymentDetailsTabRef.value?.getFormData());
const previewEquipmentDetails = computed(() => equipmentsTabRef.value?.getFormData());
const previewWorksheetDetails = computed(() => 
  worksheetsTabRef.value?.getFormData() || []
);

// Handle form submission
const submitForm = async () => {
  try {
    // Validate all tabs
    const customerDetailsValid = await customerDetailsTabRef.value?.validateForm();
    const paymentDetailsValid = await paymentDetailsTabRef.value?.validateForm();
    // const equipmentsValid = await equipmentsTabRef.value?.validateForm();
    // const worksheetsValid = await worksheetsTabRef.value?.validateForm();
    
    if (!customerDetailsValid || !paymentDetailsValid ) {
      snackbarStore.show({
        text: t('page.sales_request_form.validation_error'),
        color: 'error'
      });
      return;
    }
    
    // Get form data from all tabs
    const customerDetails = customerDetailsTabRef.value?.getFormData();
    const paymentDetails = paymentDetailsTabRef.value?.getFormData();
    const equipmentDetails = equipmentsTabRef.value?.getFormData();
    const worksheetDetails = worksheetsTabRef.value?.getFormData();
    
    // Combine all form data into the payload for the API
    // Note: The structure of this payload must match the DsdRequestInput class in the backend.
    const formData = {
      requestId: requestId.value,

      // Customer Details, Payment, and Equipment details are spread assuming
      // the getFormData() method returns keys matching the backend model.
      ...customerDetails,
      ...paymentDetails,
      ...equipmentDetails,

      // Worksheet details may require specific mapping
      requestItems: worksheetDetails?.map((item: any) => ({
        itemId: item.itemNumber?.value,
        dealerIt: item.dsdQuantity?.value,
        dsdQuantity: item.dealerIt?.value,
        msrp: item.msrp?.value,
        requestedSellingPrice: item.requestSellingPrice?.value
      }))
    };
    
    // Submit the form data
    await saveSalesRequest(formData);
    
    // Show success message
    snackbarStore.show({
      text: t('page.sales_request_form.success_message'),
      color: 'success'
    });
    
    // Redirect to sales requests page
    router.push({ name: 'pageSalesRequests' });
  } catch (error) {
    console.error('Error submitting form:', error);
    snackbarStore.show({
      text: t('page.sales_request_form.error_message'),
      color: 'error'
    });
  }
};

// Handle cancel
const cancelForm = () => {
    router.push({ name: 'pageSalesRequests' });
};

// Load data for an existing request on component mount
onMounted(async () => {
    const id = route.params.requestId as string;
    if (id) {
        requestId.value = parseInt(id, 10);
        try {
            appStore.startPageLoader();
            const response = await getSalesRequest(requestId.value);
            const data = response.data.data; // Adjust based on actual API response structure

            // NOTE: Child tabs need a method to receive and load data, e.g., setFormData(data).
            // You will need to implement this method in each respective tab component.
            if (customerDetailsTabRef.value && (customerDetailsTabRef.value as any).setFormData) {
              (customerDetailsTabRef.value as any).setFormData(data);
            }
            if (paymentDetailsTabRef.value && (paymentDetailsTabRef.value as any).setFormData) {
              (paymentDetailsTabRef.value as any).setFormData(data);
            }
            if (equipmentsTabRef.value && (equipmentsTabRef.value as any).setFormData) {
              (equipmentsTabRef.value as any).setFormData(data);
            }
            if (worksheetsTabRef.value && (worksheetsTabRef.value as any).setFormData) {
              (worksheetsTabRef.value as any).setFormData(data.requestItems);
            }

        } catch (error) {
            console.error('Error fetching request data:', error);
            snackbarStore.show({
                text: t('page.sales_request_form.error_message'),
                color: 'error'
            });
        } finally {
            appStore.stopPageLoader();
        }
    } else {
        appStore.stopPageLoader();
    }
});

const goToNextTab = () => {
    if (activeTab.value < 4) {
        activeTab.value += 1;
    }
};

// Preview data before submission
const previewData = () => {
    // Set active tab to preview
    activeTab.value = 4;
};
</script>

<template>
    <div class="pa-4">
        <div class="d-flex justify-space-between align-center mb-4">
            <h1>{{ t('page.sales_request_form.title') }}</h1>
        </div>
        
        <v-card class="mt-4">
            <!-- <v-card-title>
                {{ t('page.sales_request_form.form.title') }}
            </v-card-title> -->
            
            <v-card-text>
                <v-tabs 
                    v-model="activeTab" 
                    bg-color="canon"
                    color="white"
                    slider-color="green"
                    slider-size="14"
                >
                    <v-tab value="0">{{ t('page.sales_request_form.tabs.customer_details') }}</v-tab>
                    <v-tab value="1">{{ t('page.sales_request_form.tabs.payment_details') }}</v-tab>
                    <v-tab value="2">{{ t('page.sales_request_form.tabs.equipments') }}</v-tab>
                    <v-tab value="3">{{ t('page.sales_request_form.tabs.worksheets') }}</v-tab>
                    <v-tab value="4">Preview</v-tab>
                </v-tabs>
                
                <v-window v-model="activeTab">
                    <v-window-item value="0">
                        <CustomerDetailsTab ref="customerDetailsTabRef" />
                    </v-window-item>
                    
                    <v-window-item value="1">
                        <PaymentDetailsTab ref="paymentDetailsTabRef" />
                    </v-window-item>
                    
                    <v-window-item value="2">
                        <EquipmentsTab ref="equipmentsTabRef" />
                    </v-window-item>
                    
                    <v-window-item value="3">
                        <WorksheetsTab ref="worksheetsTabRef" />
                    </v-window-item>
                    
                    <v-window-item value="4">
                        <PreviewTab 
                            ref="previewTabRef"
                            :customer-details="previewCustomerDetails"
                            :payment-details="previewPaymentDetails"
                            :equipment-details="previewEquipmentDetails"
                            :worksheet-details="previewWorksheetDetails"
                        />
                    </v-window-item>
                </v-window>
            </v-card-text>
            
            <v-card-actions class="pa-4">
                <v-spacer></v-spacer>
                
                <v-btn 
                    color="error" 
                    variant="outlined"
                    @click="cancelForm"
                >
                    {{ t('common.cancel') }}
                </v-btn>
                
                <v-btn 
                    
                    color="primary" 
                    class="ml-2"
                    @click="goToNextTab"
                >
                    {{ t('common.next') }}
                </v-btn>
                
                <v-btn 
                    
                    color="info" 
                    class="ml-2"
                    @click="previewData"
                >
                    Preview
                </v-btn>
                
                <v-btn 
                    
                    color="success" 
                    class="ml-2"
                    @click="submitForm"
                >
                    {{ t('common.submit') }}
                </v-btn>
                    <v-btn 
                    
                    color="success" 
                    class="ml-2"
                    @click="submitForm"
                >
                    Validate Form
                </v-btn>
            </v-card-actions>
        </v-card>
    </div>
</template>


















