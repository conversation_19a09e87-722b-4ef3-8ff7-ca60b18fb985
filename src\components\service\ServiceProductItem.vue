<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, watch } from 'vue';

type RateType = 'bw' | 'colour' | 'minimum_base_amt' | 'minimum_base_volume';
type Discounts = Record<RateType, number | null>;
type PublishedRates = Record<RateType, number | null>;
type RequestedRates = Record<string, {
  fixed_service_term_length: number | null;
  bw: number | null;
  colour: number | null;
  minimum_base_amt: number | null;
  minimum_base_volume: number | null;
}>;

interface Product {
  model: string | null;
  dsd_qty: number | null;
  dealer_qty: number | null;
  estimated_amv_unit: number | null;
  colour_percentage: number | null;
  oversize_percentage: number | null;
  fees_included_in_cpc: boolean;
  published_rates: PublishedRates;
  discounts: Discounts;
  requested_rates: RequestedRates;
}

const props = defineProps<{
  modelValue: Product;
  selectedServiceValuePack: string;
}>();

const emit = defineEmits(['update:modelValue']);

const localProductData = computed({
  get: () => props.modelValue,
  set: (value: Product) => emit('update:modelValue', value)
});

// Initialize discounts if not present
if (!localProductData.value.discounts) {
  localProductData.value = {
    ...localProductData.value,
    discounts: {
      bw: 0,
      colour: 0,
      minimum_base_amt: 0,
      minimum_base_volume: 0
    }
  };
}

const productModels = [
  'imageRUNNER ADVANCE DX C5850i',
  'imageRUNNER ADVANCE DX C5860i',
  'imageRUNNER ADVANCE DX C5870i',
  'Other Model A',
  'Other Model B',
];

// Simplified published rates structure
const productModelRates: Record<string, PublishedRates> = {
  'imageRUNNER ADVANCE DX C5850i': { 
    bw: 0.010, 
    colour: 0.075, 
    minimum_base_amt: 50, 
    minimum_base_volume: 5000 
  },
  'imageRUNNER ADVANCE DX C5860i': { 
    bw: 0.011, 
    colour: 0.080, 
    minimum_base_amt: 60, 
    minimum_base_volume: 5500 
  },
  'imageRUNNER ADVANCE DX C5870i': { 
    bw: 0.012, 
    colour: 0.085, 
    minimum_base_amt: 70, 
    minimum_base_volume: 6000 
  },
  'Other Model A': { 
    bw: 0.008, 
    colour: 0.065, 
    minimum_base_amt: 40, 
    minimum_base_volume: 4000 
  },
  'Other Model B': { 
    bw: 0.009, 
    colour: 0.070, 
    minimum_base_amt: 45, 
    minimum_base_volume: 4500 
  },
};

const termOptions = [
  { title: '12 Months', value: 12 },
  { title: '24 Months', value: 24 },
  { title: '36 Months', value: 36 },
  { title: '48 Months', value: 48 },
  { title: '60 Months', value: 60 },
];

// Initialize requested_rates if not present or empty
if (!localProductData.value.requested_rates || Object.keys(localProductData.value.requested_rates).length === 0) {
  localProductData.value.requested_rates = {
    option1: createDefaultRateOption()
  };
} else {
  // Ensure we have at least one option
  const options = Object.keys(localProductData.value.requested_rates);
  if (options.length === 0) {
    localProductData.value.requested_rates.option1 = createDefaultRateOption();
  }
}

function createDefaultRateOption() {
  return {
    fixed_service_term_length: 36, // Default to 36 months
    bw: null,
    colour: null,
    minimum_base_amt: null,
    minimum_base_volume: null
  };
}

// Watch for changes in model or discounts to update requested rates
watch(
  [
    () => localProductData.value.model,
    () => localProductData.value.discounts,
    () => localProductData.value.requested_rates.option1.fixed_service_term_length
  ],
  ([newModel, newDiscounts, newTerm]) => {
    if (!newModel) return;
    
    const published = productModelRates[newModel];
    if (!published) return;
    
    // Update published rates
    localProductData.value.published_rates = { ...published };
    
    // Calculate and update requested rates for each service option
    Object.keys(localProductData.value.requested_rates).forEach(optionKey => {
      const option = localProductData.value.requested_rates[optionKey];
      Object.keys(published).forEach(rateKey => {
        const rateType = rateKey as RateType;
        option[rateType] = calculateRequestedRate(
          published[rateType],
          localProductData.value.discounts[rateType]
        );
      });
    });
  },
  { deep: true, immediate: true }
);

// Watch for discount changes to update requested rates
watch(
  () => localProductData.value.discounts,
  (newDiscounts) => {
    if (!localProductData.value.model) return;
    
    const published = productModelRates[localProductData.value.model];
    if (!published) return;
    
    // Update requested rates based on new discounts
    Object.keys(localProductData.value.requested_rates).forEach(optionKey => {
      const option = localProductData.value.requested_rates[optionKey];
      Object.keys(published).forEach(rateKey => {
        const rateType = rateKey as RateType;
        option[rateType] = calculateRequestedRate(
          published[rateType],
          newDiscounts[rateType]
        );
      });
    });
  },
  { deep: true }
);

const addServiceOption = () => {
  const optionCount = Object.keys(localProductData.value.requested_rates).length;
  if (optionCount >= 4) return;
  
  const newOptionKey = `option${optionCount + 1}`;
  localProductData.value.requested_rates[newOptionKey] = createDefaultRateOption();
  
  // Update requested rates for the new option based on current discounts
  updateRequestedRates();
};

const removeServiceOption = (optionKey: string) => {
  if (Object.keys(localProductData.value.requested_rates).length <= 1) return;
  delete localProductData.value.requested_rates[optionKey];
};

const requiredRule = (value: any) => !!value || 'This field is required.';

// Format number to 5 decimal places for display
const formatRate = (value: number | null): string => {
  if (value === null || value === undefined) return '-';
  const numValue = Number(value);
  if (isNaN(numValue)) return '-';
  return numValue.toFixed(5).replace(/\.?0+$/, '');
};

// Format currency for display
const formatCurrency = (value: number | null): string => {
  if (value === null) return '-';
  return `$${value.toFixed(2)}`;
};

// Format volume for display
const formatVolume = (value: number | null): string => {
  if (value === null) return '-';
  return value.toLocaleString();
};

// Calculate requested rate based on base rate and discount
const calculateRequestedRate = (baseRate: number | null, discount: number | null): number | null => {
  if (baseRate === null || discount === null) return null;
  const rate = baseRate * (1 - (discount / 100));
  return parseFloat(rate.toFixed(5));
};

// Update requested rates when discounts change
const updateRequestedRates = () => {
  if (!localProductData.value.model) return;
  
  const published = productModelRates[localProductData.value.model];
  if (!published) return;
  
  // Update requested rates for each service option
  Object.keys(localProductData.value.requested_rates).forEach(optionKey => {
    const option = localProductData.value.requested_rates[optionKey];
    Object.keys(published).forEach(rateKey => {
      const rateType = rateKey as RateType;
      option[rateType] = calculateRequestedRate(
        published[rateType],
        localProductData.value.discounts[rateType]
      );
    });
  });
};

// Watch for discount changes and update requested rates
watch(
  () => localProductData.value.discounts,
  () => {
    updateRequestedRates();
  },
  { deep: true }
);

// Watch for model changes and update published rates
watch(
  () => localProductData.value.model,
  (newModel) => {
    if (!newModel) return;
    
    const published = productModelRates[newModel];
    if (!published) return;
    
    // Update published rates
    localProductData.value.published_rates = { ...published };
    
    // Update requested rates based on new published rates and current discounts
    updateRequestedRates();
  },
  { immediate: true }
);

</script>

<template>
  <v-container fluid class="pa-0">
    <!-- I. Models Overview -->
    <v-card variant="tonal" class="mb-3">
      <v-card-title class="text-subtitle-2">Models Overview</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="4">
            <v-select
              v-model="localProductData.model"
              :items="productModels"
              label="Model"
              density="compact"
              clearable
              :rules="[requiredRule]"
            />
          </v-col>
          <v-col cols="6" md="2">
            <v-text-field 
              v-model.number="localProductData.dsd_qty" 
              label="DSD Qty" 
              type="number" 
              density="compact" 
              :rules="[requiredRule]"
            />
          </v-col>
          <v-col cols="6" md="2">
            <v-text-field 
              v-model.number="localProductData.dealer_qty" 
              label="Dealer QTY" 
              type="number" 
              density="compact" 
              :rules="[requiredRule]"
            />
          </v-col>
        </v-row>
        <v-row dense>
          <v-col cols="12" md="4">
            <v-text-field 
              v-model.number="localProductData.estimated_amv_unit" 
              label="Estimated AMV / Unit *" 
              type="number" 
              prefix="$" 
              density="compact" 
              :rules="[requiredRule]" 
            />
          </v-col>
          <v-col cols="6" md="2">
            <v-text-field 
              v-model.number="localProductData.colour_percentage" 
              label="Colour % *" 
              type="number" 
              suffix="%" 
              density="compact" 
              :rules="[requiredRule]"
            />
          </v-col>
          <v-col cols="6" md="2">
            <v-text-field 
              v-model.number="localProductData.oversize_percentage" 
              label="Oversize % *" 
              type="number" 
              suffix="%" 
              density="compact" 
              :rules="[requiredRule]"
            />
          </v-col>
          <v-col cols="12" md="2" class="d-flex align-center">
            <v-checkbox 
              v-model="localProductData.fees_included_in_cpc" 
              label="Fees Included in CPC" 
              density="compact" 
              hide-details 
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- II. Published Rates with Discounts -->
    <v-card variant="tonal" class="mb-3">
      <v-card-title class="text-subtitle-2">Published Rates & Discounts</v-card-title>
      <v-card-text>
        <v-table density="compact" v-if="localProductData.model && localProductData.published_rates">
          <thead>
            <tr>
              <th class="text-left" style="width: 25%">Rate Type</th>
              <th class="text-right" style="width: 35%; padding-right: 16px">Published Rate</th>
              <th class="text-right" style="width: 40%">Discount %</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-left font-weight-medium">B&W</td>
              <td class="text-right pr-4">
                <span class="text-h6 font-weight-bold">{{ formatRate(localProductData.published_rates.bw) }}</span>
              </td>
              <td class="pl-4">
                <v-text-field
                  v-model.number="localProductData.discounts.bw"
                  density="compact"
                  hide-details
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  suffix="%"
                  @update:model-value="updateRequestedRates"
                />
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-medium">Colour</td>
              <td class="text-right pr-4">
                <span class="text-h6 font-weight-bold">{{ formatRate(localProductData.published_rates.colour) }}</span>
              </td>
              <td class="pl-4">
                <v-text-field
                  v-model.number="localProductData.discounts.colour"
                  density="compact"
                  hide-details
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  suffix="%"
                  @update:model-value="updateRequestedRates"
                />
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-medium">Min. Base $</td>
              <td class="text-right pr-4">
                <span class="text-h6 font-weight-bold">{{ formatCurrency(localProductData.published_rates.minimum_base_amt) }}</span>
              </td>
              <td class="pl-4">
                <v-text-field
                  v-model.number="localProductData.discounts.minimum_base_amt"
                  density="compact"
                  hide-details
                  type="number"
                  step="1"
                  min="0"
                  max="100"
                  suffix="%"
                  @update:model-value="updateRequestedRates"
                />
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-medium">Min. Base Vol.</td>
              <td class="text-right pr-4">
                <span class="text-h6 font-weight-bold">{{ formatVolume(localProductData.published_rates.minimum_base_volume) }}</span>
              </td>
              <td class="pl-4">
                <v-text-field
                  v-model.number="localProductData.discounts.minimum_base_volume"
                  density="compact"
                  hide-details
                  type="number"
                  step="1"
                  min="0"
                  max="100"
                  suffix="%"
                  @update:model-value="updateRequestedRates"
                />
              </td>
            </tr>
          </tbody>
        </v-table>
        <p v-else class="text-caption grey--text">Select a model to view its published rates.</p>
      </v-card-text>
    </v-card>

    <!-- III. Service Options -->
    <v-card variant="tonal" class="mb-3">
      <v-card-title class="d-flex justify-space-between align-center">
        <span class="text-subtitle-2">Service Options</span>
        <v-btn
          v-if="Object.keys(localProductData.requested_rates).length < 4"
          size="small"
          color="primary"
          variant="text"
          prepend-icon="add"
          @click="addServiceOption"
          :disabled="!localProductData.model"
          class="ml-2"
        >
          Add Option ({{ Object.keys(localProductData.requested_rates).length }}/4)
        </v-btn>
      </v-card-title>
      
      <v-card-text>
        <v-table density="compact" v-if="localProductData.model">
          <thead>
            <tr>
              <th class="text-center">Option</th>
              <th class="text-center">Term Length</th>
              <th class="text-center" style="width: 150px;">B&W Rate</th>
              <th class="text-center" style="width: 150px;">Colour Rate</th>
              <th class="text-center" style="width: 150px;">Min. Base $</th>
              <th class="text-center" style="width: 150px;">Min. Base Vol.</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(option, key) in localProductData.requested_rates" :key="key">
              <td>Option {{ key.replace('option', '') }}</td>
              <td>
                <v-select
                  v-model="option.fixed_service_term_length"
                  :items="termOptions"
                  item-title="title"
                  item-value="value"
                  density="compact"
                  hide-details
                  style="min-width: 120px;"
                  :rules="[requiredRule]"
                  return-object
                />
              </td>
              <td class="text-center">
                <v-text-field
                  v-model.number="option.bw" 
                  density="compact"
                  hide-details
                  type="number"
                  step="0.00001"
                  @update:model-value="updateRequestedRates"
                />
              </td>
              <td class="text-center">
                <v-text-field
                  v-model.number="option.colour"
                  density="compact"
                  hide-details
                  type="number"
                  step="0.00001"
                  @update:model-value="updateRequestedRates"
                />
              </td>
              <td class="text-center">
                <v-text-field
                  v-model.number="option.minimum_base_amt"
                  density="compact"
                  hide-details
                  type="number"
                  step="0.01"
                  prefix="$"
                  @update:model-value="updateRequestedRates"
                />
              </td>
              <td class="text-center">
                <v-text-field
                  v-model.number="option.minimum_base_volume"
                  density="compact"
                  hide-details
                  type="number"
                  step="1"
                  @update:model-value="updateRequestedRates"
                />
              </td>
              <td class="text-center">
                <v-btn
                  v-if="Object.keys(localProductData.requested_rates).length > 1"
                  icon
                  size="small"
                  variant="text"
                  color="error"
                  @click="removeServiceOption(key)"
                >
                  <v-icon>delete</v-icon>
                </v-btn>
              </td>
            </tr>
          </tbody>
        </v-table>
        <p v-else class="text-caption grey--text">Select a model to configure service options.</p>
      </v-card-text>
    </v-card>

  </v-container>
</template>

<style scoped>
/* Add any component-specific styles here if needed */
.v-table {
  --v-table-header-height: 40px;
}

.v-table th {
  font-weight: 600;
  letter-spacing: 0.3px;
}

.v-table td {
  padding: 0 8px;
  height: 52px;
}

.v-table .v-input {
  font-size: 0.9rem;
}

.v-table .v-input__control {
  min-height: 36px;
}
</style>
