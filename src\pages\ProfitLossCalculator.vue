<template>
  <v-container fluid class="pnl-page">
    <!-- Header Section -->
    <v-row class="mb-4 header-section align-center">
      <v-col cols="12" md="4">
        <v-text-field
          v-model="pnlData.customerName"
          label="Customer Name"
          variant="outlined"
          density="compact"
          hide-details
        ></v-text-field>
      </v-col>
      <v-col cols="12" md="2">
        <v-select
          v-model="pnlData.categoryInfo.category"
          :items="['Special SAS', 'Education', 'Government']"
          label="Category"
          variant="outlined"
          density="compact"
          hide-details
        ></v-select>
      </v-col>
      <v-col cols="12" md="6" class="text-right">
        <v-btn
          color="primary"
          prepend-icon="download"
          @click="exportToCSV"
        >
          Export to CSV
        </v-btn>
      </v-col>
    </v-row>

    <!-- Deal Summary -->
    <v-card class="mb-6 deal-summary-card" outlined>
      <v-card-title class="text-h6">Deal Summary</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Total Quantity</div>
            <div class="value">{{ dealSummary.totalQuantity }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>MSRP</div>
            <div class="value">{{ formatCurrency(dealSummary.totalMSRP) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Requested Selling Price</div>
            <div class="value highlighted-summary-value">{{ formatCurrency(dealSummary.totalRequestedSellingPrice) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Percentage of MSRP</div>
            <div class="value">{{ dealSummary.percentageOfMSRP.toFixed(1) }}%</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Cost</div>
            <div class="value">{{ formatCurrency(dealSummary.totalProductCost) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Total Cost</div>
            <div class="value highlighted-summary-value">{{ formatCurrency(dealSummary.overallTotalCost) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Total CMAC Support Amount</div>
            <div class="value">{{ formatCurrency(dealSummary.grandTotalCmacSupportAmount) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>Total CMAC Cost</div>
            <div class="value">{{ formatCurrency(dealSummary.grandTotalCMACCost) }}</div>
          </v-col>
          <v-col cols="12" sm="6" md="4" lg="4">
            <div>GP %</div>
            <div class="value" :class="dealSummary.gpPercentage >= 0 ? 'text-green' : 'text-red'">{{ dealSummary.gpPercentage.toFixed(1) }}%</div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Global Controls -->
    <v-card class="mb-4" outlined>
      <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
         Adjustments
      </v-card-title>
      <v-card-text>
        <v-row dense align="center">
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              v-model.number="globalDiscount"
              label="Discount"
              type="number"
              variant="outlined"
              density="compact"
              hide-details
              step="0.1"
              min="0"
              max="100"
              suffix="%"
              @update:model-value="debouncedApplyGlobalDiscount"
            </v-text-field>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              v-model.number="globalTargetGP"
              label="Target GP %"
              type="number"
              variant="outlined"
              density="compact"
              hide-details
              step="0.1"
              min="-100"
              max="100"
              suffix="%"
              @update:model-value="debouncedApplyGlobalTargetGP"
            </v-text-field>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-btn
              color="secondary"
              variant="outlined"
              prepend-icon="refresh"
              @click="resetGlobalValues"
              block
            >
              Reset All
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Product Groups -->
    <div v-for="(group, groupIndex) in pnlData.productGroups" :key="groupIndex" class="mb-6">
      <v-card outlined>
        <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
          {{ group.groupName }}
        </v-card-title>
        <div style="overflow-x: auto; width: 100%;">
          <v-table density="compact">
            <thead>
              <tr>
                <th>Product</th>
                <th>Item Number</th>
                <th>BSD Qty</th>
                <th>Dealer Its</th>
                <th>Total Qty</th>
                <th>MSRP</th>
                <th>Req. Selling Price (Unit)</th>
                <th>Total Req. Selling Price</th>
                <th>% of MSRP</th>
                <th>Cost (Unit)</th>
                <th>Total Cost</th>
                <th>CMAC Support (Unit)</th>
                <th class="support-percentage-column">Support %</th>
                <th>CMAC Cost (Unit)</th>
                <th>Total CMAC Cost</th>
                <th>GP %</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, itemIndex) in group.items" :key="`item-${itemIndex}`">
                <td>{{ item.product }}</td>
                <td>{{ item.itemNumber }}</td>
                <td>{{ item.bsdQuantity }}</td>
                <td>{{ item.dealerIts }}</td>
                <td>{{ (item.bsdQuantity || 0) + (item.dealerIts || 0) }}</td>
                <td>{{ formatCurrency(item.msrp) }}</td>
                <td>
                  <v-text-field
                    v-model.number="item.unitRequestedSellingPrice"
                    type="number"
                    variant="underlined"
                    density="compact"
                    hide-details
                    step="0.01"
                    min="0"
                    @update:model-value="updateSellingPrice(item)"
                  />
                </td>
                <td>{{ formatCurrency(item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                <td>{{ item.msrp && item.unitRequestedSellingPrice ? ((item.unitRequestedSellingPrice / item.msrp) * 100).toFixed(0) + '%' : 'N/A' }}</td>
                <td>{{ formatCurrency(item.unitCost) }}</td>
                <td>{{ formatCurrency(item.unitCost * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                <td>
                  <v-text-field 
                    v-model.number="item.cmacSupportValue" 
                    type="number" 
                    variant="underlined" 
                    density="compact" 
                    hide-details
                    step="0.01"
                    min="0"
                    @update:model-value="updateFromSupportValue(item)"
                  />
                </td>
                <td class="support-percentage-column">
                  <v-text-field 
                    v-model.number="item.supportPercentage" 
                    type="number" 
                    variant="underlined" 
                    density="compact" 
                    hide-details
                    step="0.01"
                    min="0"
                    max="100"
                    suffix="%"
                    @update:model-value="updateFromSupportPercentage(item)"
                  />
                </td>
                <td>{{ formatCurrency(item.unitCost - item.cmacSupportValue) }}</td>
                <td>{{ formatCurrency((item.unitCost - item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                <td :class="{'text-red': (((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) / ((item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) || 1) * 100) < 0}">
                  {{ (item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) > 0 ? ((((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) / (item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0)))) * 100).toFixed(1) + '%' : 'N/A' }}
                </td>
              </tr>

            </tbody>
            <tfoot>
              <tr class="font-weight-bold group-totals-row">
                <td colspan="2">Group Totals:</td>
                <td>{{ getGroupTotals(group).totalBsdQuantity }}</td>
                <td>{{ getGroupTotals(group).totalDealerIts }}</td>
                <td>{{ getGroupTotals(group).totalQuantity }}</td>
                <td>{{ formatCurrency(getGroupTotals(group).totalMSRP) }}</td>
                <td>{{" "}}</td>
                <td>{{ formatCurrency(getGroupTotals(group).totalRequestedSellingPrice) }}</td>
                <td>{{ getGroupTotals(group).avgPercentageOfMSRP.toFixed(0) }}%</td>
                <td>{{" "}}</td>
                <td>{{ formatCurrency(getGroupTotals(group).totalItemCost) }}</td>
                <td>{{" "}}</td>
                <td>{{" "}}</td>
                <td>{{" "}}</td>
                <td>{{ formatCurrency(getGroupTotals(group).totalGroupCMACCost) }}</td>
                <td>{{ getGroupTotals(group).groupGPPercentage.toFixed(1) }}%</td>
              </tr>
            </tfoot>
          </v-table>
        </div>
      </v-card>

    </div>

    <!-- Final Summary Section -->
    <v-card class="mt-6 final-summary-card" outlined>
      <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
        Deal Cost Summary & GP
      </v-card-title>
      <v-list dense class="pa-0">
        <v-list-item>
          <v-row no-gutters>
            <v-col cols="8">Product Table Total CMAC Support Amount:</v-col>
            <v-col cols="4" class="text-right">{{ formatCurrency(dealSummary.grandTotalCmacSupportAmount) }}</v-col>
          </v-row>
        </v-list-item>
        <v-divider class="my-2"></v-divider>
        <v-list-item class="font-weight-bold">
          <v-row no-gutters>
            <v-col cols="8">Subtotal of Above Costs:</v-col>
            <v-col cols="4" class="text-right">{{ formatCurrency(dealSummary.subtotalOfAboveCosts) }}</v-col>
          </v-row>
        </v-list-item>
        <v-divider class="my-2"></v-divider>
        <v-list-item class="font-weight-bold success--text">
          <v-row no-gutters>
            <v-col cols="8">Overall GP %:</v-col>
            <v-col cols="4" class="text-right">{{ dealSummary.gpPercentage.toFixed(2) }}%</v-col>
          </v-row>
        </v-list-item>
      </v-list>
    </v-card>

  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue';
import { saveAs } from 'file-saver';
import { debounce } from 'lodash';

interface PNLItem {
  product: string;
  itemNumber: string;
  bsdQuantity: number;
  dealerIts: number;
  msrp: number;
  unitRequestedSellingPrice: number;
  unitCost: number;
  cmacSupportValue: number;
  supportPercentage: number;
  // Add any other fields that might be needed for calculations
}

interface ProductGroup {
  groupName: string;
  items: PNLItem[];
}

interface PNLData {
  customerName: string;
  categoryInfo: {
    category: string;
  };
  productGroups: ProductGroup[];
}

const pnlData = ref<PNLData>({
  customerName: "ENTERA UTILITY CONTRACTORS CO LIMITED",
  categoryInfo: { category: 'Special SAS' },
  productGroups: [
    {
      groupName: "imageRUNNER ADVANCE DX C5850i",
      items: [
        { product: "imageRUNNER ADVANCE DX C5850i", itemNumber: "3826C002AA", bsdQuantity: 3, dealerIts: 0, msrp: 29797, unitRequestedSellingPrice: 6777, unitCost: 13554, cmacSupportValue: 6777, supportPercentage: 50 },
        { product: "MAX ImagePro 15A", itemNumber: "4280V345", bsdQuantity: 3, dealerIts: 0, msrp: 248, unitRequestedSellingPrice: 165, unitCost: 165, cmacSupportValue: 0, supportPercentage: 0 },
        { product: "Cassette Feeding Unit-AQ1", itemNumber: "4030C002BA", bsdQuantity: 3, dealerIts: 0, msrp: 1871, unitRequestedSellingPrice: 550.50, unitCost: 1101, cmacSupportValue: 550.50, supportPercentage: 50 },
      ],
    },
    {
      groupName: "uniFLOW for SMB - MEAP License",
      items: [
        { product: "uniFLOW for SMB - MEAP License", itemNumber: "3575B345AA", bsdQuantity: 4, dealerIts: 0, msrp: 1177, unitRequestedSellingPrice: 743.94, unitCost: 785, cmacSupportValue: 78.50, supportPercentage: 10 },
        { product: "Advanced Workflow and Device", itemNumber: "3575B490AA", bsdQuantity: 4, dealerIts: 0, msrp: 35, unitRequestedSellingPrice: 15.40, unitCost: 22, cmacSupportValue: 6.60, supportPercentage: 30 },
      ],
    }
  ]
});

const formatCurrency = (value: number) => {
  if (typeof value !== 'number' || isNaN(value)) return '$0.00';
  return value.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
};

const getGroupTotals = (group: ProductGroup) => {
  let totalBsdQuantity = 0;
  let totalDealerIts = 0;
  let totalQuantity = 0;
  let totalMSRP = 0;
  let totalRequestedSellingPrice = 0;
  let totalItemCost = 0;
  let totalCmacSupportAmount = 0;
  let totalGroupCMACCost = 0;

  group.items.forEach(item => {
    const itemTotalQuantity = (item.bsdQuantity || 0) + (item.dealerIts || 0);
    totalBsdQuantity += (item.bsdQuantity || 0);
    totalDealerIts += (item.dealerIts || 0);
    totalQuantity += itemTotalQuantity;
    totalMSRP += item.msrp * itemTotalQuantity;
    totalRequestedSellingPrice += item.unitRequestedSellingPrice * itemTotalQuantity;
    totalItemCost += item.unitCost * itemTotalQuantity;
    totalCmacSupportAmount += item.cmacSupportValue * itemTotalQuantity;
    totalGroupCMACCost += (item.unitCost - item.cmacSupportValue) * itemTotalQuantity;
  });

  const groupGrossProfit = totalRequestedSellingPrice - totalItemCost + totalCmacSupportAmount;
  const groupGPPercentage = totalRequestedSellingPrice !== 0 ? (groupGrossProfit / totalRequestedSellingPrice) * 100 : 0;
  const avgPercentageOfMSRP = totalMSRP !== 0 ? (totalRequestedSellingPrice / totalMSRP) * 100 : 0;

  return {
    totalBsdQuantity,
    totalDealerIts,
    totalQuantity,
    totalMSRP,
    totalRequestedSellingPrice,
    avgPercentageOfMSRP,
    totalItemCost: totalItemCost,
    totalCmacSupportAmount,
    totalGroupCMACCost,
    totalGroupGP: groupGrossProfit,
    groupGPPercentage,
  };
};

const updateSellingPrice = (item: PNLItem) => {
  if (item.msrp && item.unitRequestedSellingPrice >= 0) {
    // Calculate the discount percentage from MSRP
    const discountPercentage = ((item.msrp - item.unitRequestedSellingPrice) / item.msrp) * 100;
    item.supportPercentage = Math.max(0, Math.min(100, discountPercentage));
    
    // Update CMAC support value based on the new selling price
    item.cmacSupportValue = Math.max(0, item.unitCost - item.unitRequestedSellingPrice);
  }
  recalculate();
};

const updateFromSupportValue = (item: PNLItem) => {
  if (item.cmacSupportValue >= 0) {
    // Calculate new selling price based on CMAC support
    item.unitRequestedSellingPrice = Math.max(0, item.unitCost - item.cmacSupportValue);
    
    // Update percentage based on MSRP if available
    if (item.msrp && item.msrp > 0) {
      item.supportPercentage = ((item.msrp - item.unitRequestedSellingPrice) / item.msrp) * 100;
    }
  }
  recalculate();
};

const updateFromSupportPercentage = (item: PNLItem) => {
  if (item.supportPercentage >= 0 && item.supportPercentage <= 100) {
    // Calculate new selling price based on support percentage
    if (item.msrp && item.msrp > 0) {
      item.unitRequestedSellingPrice = item.msrp * (1 - (item.supportPercentage / 100));
      item.cmacSupportValue = Math.max(0, item.unitCost - item.unitRequestedSellingPrice);
    }
  }
  recalculate();
};

// Global controls state
const globalDiscount = ref(0);
const globalTargetGP = ref(0);

// Apply global discount to all products
const applyGlobalDiscount = () => {
  pnlData.value.productGroups.forEach(group => {
    group.items.forEach(item => {
      if (item.msrp && item.unitCost) {
        // Calculate new selling price based on discount
        const newSellingPrice = item.msrp * (1 - globalDiscount.value / 100);
        item.unitRequestedSellingPrice = Math.max(0, newSellingPrice);
        
        // Update support values
        updateSellingPrice(item);
      }
    });
  });
};

// Apply target GP to all products
const applyGlobalTargetGP = () => {
  pnlData.value.productGroups.forEach(group => {
    group.items.forEach(item => {
      if (item.unitCost && item.msrp) {
        // Calculate required selling price to achieve target GP
        const targetGP = globalTargetGP.value / 100;
        const requiredSellingPrice = item.unitCost / (1 - targetGP);
        
        // Don't let selling price exceed MSRP
        item.unitRequestedSellingPrice = Math.min(requiredSellingPrice, item.msrp);
        
        // Update support values
        updateSellingPrice(item);
      }
    });
  });
};

// Debounced update functions
const debouncedApplyGlobalDiscount = debounce(applyGlobalDiscount, 300);
const debouncedApplyGlobalTargetGP = debounce(applyGlobalTargetGP, 300);

// Clean up debounce on component unmount
onUnmounted(() => {
  debouncedApplyGlobalDiscount.cancel();
  debouncedApplyGlobalTargetGP.cancel();
});

// Reset all global values
const resetGlobalValues = () => {
  globalDiscount.value = 0;
  globalTargetGP.value = 0;
  
  // Reset all products to their original MSRP
  pnlData.value.productGroups.forEach(group => {
    group.items.forEach(item => {
      if (item.msrp) {
        item.unitRequestedSellingPrice = item.msrp;
        updateSellingPrice(item);
      }
    });
  });
};

const recalculate = () => {
  // Any global recalculation logic can go here
  // Currently handled by computed properties and individual field updates
};

const exportToCSV = () => {
  // Create CSV header
  const headers = [
    'Group',
    'Product',
    'Item Number',
    'BSD Qty',
    'Dealer Its',
    'Total Qty',
    'MSRP',
    'Req. Selling Price (Unit)',
    'Total Req. Selling Price',
    '% of MSRP',
    'Cost (Unit)',
    'Total Cost',
    'CMAC Support (Unit)',
    'Support %',
    'CMAC Cost (Unit)',
    'Total CMAC Cost',
    'GP %'
  ];

  // Create CSV rows
  const rows = pnlData.value.productGroups.flatMap(group => 
    group.items.map(item => ({
      group: group.groupName,
      product: item.product,
      itemNumber: item.itemNumber,
      bsdQty: item.bsdQuantity,
      dealerIts: item.dealerIts,
      totalQty: (item.bsdQuantity || 0) + (item.dealerIts || 0),
      msrp: item.msrp,
      unitRequestedSellingPrice: item.unitRequestedSellingPrice,
      totalRequestedSellingPrice: item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0)),
      percentOfMSRP: item.msrp && item.unitRequestedSellingPrice ? 
        (item.unitRequestedSellingPrice / item.msrp) * 100 : 0,
      unitCost: item.unitCost,
      totalCost: item.unitCost * ((item.bsdQuantity || 0) + (item.dealerIts || 0)),
      cmacSupport: item.cmacSupportValue,
      supportPercentage: item.supportPercentage,
      cmacCost: item.unitCost - item.cmacSupportValue,
      totalCmacCost: (item.unitCost - item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0)),
      gpPercentage: (item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) > 0 ? 
        (((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) * 
        ((item.bsdQuantity || 0) + (item.dealerIts || 0))) / 
        (item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0)))) * 100 : 0
    }))
  );

  // Convert to CSV
  const csvContent = [
    headers.join(','),
    ...rows.map(row => Object.values(row).map(value => 
      typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
    ).join(','))
  ].join('\r\n');

  // Create blob and download
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  saveAs(blob, `P&L_Export_${new Date().toISOString().slice(0, 10)}.csv`);
};

const dealSummary = computed(() => {
  let grandTotalBsdQuantity = 0;
  let grandTotalDealerIts = 0;
  let grandTotalQuantity = 0;
  let grandTotalMSRP = 0;
  let grandTotalRequestedSellingPrice = 0;
  let grandTotalProductCost = 0;
  let grandTotalCmacSupportAmount = 0;
  let grandTotalCMACCost = 0;
  pnlData.value.productGroups.forEach(group => {
    group.items.forEach(item => {
      const itemTotalQuantity = (item.bsdQuantity || 0) + (item.dealerIts || 0);
      grandTotalBsdQuantity += (item.bsdQuantity || 0);
      grandTotalDealerIts += (item.dealerIts || 0);
      grandTotalQuantity += itemTotalQuantity;
      grandTotalMSRP += item.msrp * itemTotalQuantity;
      grandTotalRequestedSellingPrice += item.unitRequestedSellingPrice * itemTotalQuantity;
      grandTotalProductCost += item.unitCost * itemTotalQuantity;
      grandTotalCmacSupportAmount += item.cmacSupportValue * itemTotalQuantity;
      grandTotalCMACCost += (item.unitCost - item.cmacSupportValue) * itemTotalQuantity;
    });
  });

  const overallTotalCost = grandTotalProductCost;
  const dealGrossProfit = grandTotalRequestedSellingPrice - grandTotalProductCost + grandTotalCmacSupportAmount;
  const finalDealGrossProfit = grandTotalRequestedSellingPrice - overallTotalCost + grandTotalCmacSupportAmount;
  const gpPercentage = grandTotalRequestedSellingPrice !== 0 ? (finalDealGrossProfit / grandTotalRequestedSellingPrice) * 100 : 0;
  const percentageOfMSRP = grandTotalMSRP !== 0 ? (grandTotalRequestedSellingPrice / grandTotalMSRP) * 100 : 0;

  return {
    totalQuantity: grandTotalQuantity,
    totalMSRP: grandTotalMSRP,
    totalRequestedSellingPrice: grandTotalRequestedSellingPrice,
    percentageOfMSRP,
    totalProductCost: grandTotalProductCost,
    overallTotalCost,
    sumOfUnitCmacSupportValues: grandTotalCmacSupportAmount,
    totalCmacSupport: grandTotalCmacSupportAmount,
    grandTotalCmacSupportAmount,
    grandTotalCMACCost,
    gpPercentage,
    subtotalOfAboveCosts: grandTotalCmacSupportAmount,
    finalTotalDealCost: overallTotalCost - grandTotalCmacSupportAmount,
    grossProfitDollars: finalDealGrossProfit,
  };
});
</script>

<style scoped>
.pnl-page {
  font-family: Arial, sans-serif;
  font-size: 0.9rem;
}

.header-section .v-input {
  margin-bottom: 0;
}

.deal-summary-card .v-col > div:first-child {
  font-weight: bold;
  color: #555;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.deal-summary-card .value {
  font-size: 1rem;
  font-weight: 500;
}

.deal-summary-card .value {
  font-weight: bold;
  font-size: 1.1em;
}

.deal-summary-card .highlighted-summary-value {
  background-color: #FFF9C4;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #FBC02D;
  display: inline-block;
}

.v-table {
  font-size: 0.85rem;
}

.text-red {
  color: red;
}

.text-green {
  color: green;
}

.group-totals-row td {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center; /* Ensure group totals are centered */
}

.final-summary-card .v-list-item {
  min-height: 36px;
}

.support-percentage-column {
  min-width: 90px; /* Adjust as needed */
}

.v-table thead th {
  white-space: nowrap;
}

.final-summary-card .v-divider {
  border-color: rgba(0, 0, 0, 0.12);
}

.id-commission-card,
.product-group-card {
  border: 1px solid #e0e0e0;
}

.additional-cost-row td,
.id-commission-row td {
  font-style: italic;
  background-color: #f9f9f9;
}

.v-table thead th,
.v-table tbody td {
  text-align: center;
}

.italic-description input,
.italic-description .v-label {
  font-style: italic !important;
}

.highlighted-summary-value {
  background-color: #FFF9C4;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #FBC02D;
  display: inline-block;
}

.additional-cost-row .italic-description,
.id-commission-row .italic-description {
  padding-left: 8px;
}

.additional-cost-row td:nth-child(2),
.id-commission-row td:nth-child(2) {
  font-size: 0.8em;
  color: #555;
  text-align: center;
}
/* Hide spinners from number input fields */
:deep(input[type=number])::-webkit-outer-spin-button,
:deep(input[type=number])::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

:deep(input[type=number]) {
  -moz-appearance: textfield; /* Firefox */
}
</style>
