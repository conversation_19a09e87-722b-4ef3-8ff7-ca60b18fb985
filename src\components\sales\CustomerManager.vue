<script setup lang="ts">
/**
 * @file Customer Manager component for managing customer information.
 * @version 1.0.0
 * @since 1.0.0
 */

import { ref, reactive, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

// Add language support
const { t } = useI18n();

// Define the Customer interface
interface Customer {
    id: string;
    businessName: string;
    legalName: string;
    salesforceOppId: string;
    customerRelationship: string;
    customerWebsite: string;
}

// Props
const props = defineProps<{
    modelValue: string;
    error: string;
}>();

// Emits
const emit = defineEmits(['update:modelValue', 'validate']);

// Sample customers (in a real app, these would come from an API)
const customers = ref<Customer[]>([
    {
        id: '1',
        businessName: 'Acme Corporation',
        legalName: 'Acme Inc.',
        salesforceOppId: 'OPP-12345',
        customerRelationship: 'Strong',
        customerWebsite: 'https://acme.example.com'
    },
    {
        id: '2',
        businessName: 'Globex Industries',
        legalName: 'Globex International Ltd.',
        salesforceOppId: 'OPP-67890',
        customerRelationship: 'Good',
        customerWebsite: 'https://globex.example.com'
    }
]);

// Search term for autocomplete
const searchTerm = ref('');

// Dialog state
const showDialog = ref(false);

// New customer form
const newCustomer = reactive<Omit<Customer, 'id'>>({
    businessName: '',
    legalName: '',
    salesforceOppId: '',
    customerRelationship: '',
    customerWebsite: ''
});

// Form validation state
const formErrors = reactive({
    businessName: '',
    customerRelationship: '',
    customerWebsite: ''
});

// Customer relationship options
const customerRelationshipOptions = ['Good', 'Strong', 'Fair', 'Weak'];

// Validate the form
const validateForm = () => {
    let isValid = true;
    
    // Reset errors
    Object.keys(formErrors).forEach(key => {
        formErrors[key as keyof typeof formErrors] = '';
    });
    
    // Validate required fields
    if (!newCustomer.businessName.trim()) {
        formErrors.businessName = 'Business name is required';
        isValid = false;
    }
    
    if (!newCustomer.customerRelationship) {
        formErrors.customerRelationship = 'Customer relationship is required';
        isValid = false;
    }
    
    if (!newCustomer.customerWebsite.trim()) {
        formErrors.customerWebsite = 'Customer website is required';
        isValid = false;
    } else {
        // Basic URL validation
        try {
            new URL(newCustomer.customerWebsite.startsWith('http') ? newCustomer.customerWebsite : `https://${newCustomer.customerWebsite}`);
        } catch {
            formErrors.customerWebsite = 'Invalid website URL';
            isValid = false;
        }
    }
    
    return isValid;
};

// Add a new customer
const addCustomer = () => {
    if (validateForm()) {
        // Generate a unique ID (in a real app, this would come from the backend)
        const id = Date.now().toString();
        
        // Add the new customer to the list
        customers.value.push({
            id,
            ...newCustomer
        });
        
        // Select the new customer
        emit('update:modelValue', id);
        
        // Close the dialog
        showDialog.value = false;
        
        // Reset the form
        Object.keys(newCustomer).forEach(key => {
            newCustomer[key as keyof typeof newCustomer] = '';
        });
    }
};

// Handle customer selection
const handleCustomerChange = (value: string) => {
    emit('update:modelValue', value);
    emit('validate');
};

// Filtered customers for autocomplete
const filteredCustomers = computed(() => {
    if (!searchTerm.value) return customers.value;
    
    const search = searchTerm.value.toLowerCase();
    return customers.value.filter(customer => 
        customer.businessName.toLowerCase().includes(search) ||
        customer.legalName.toLowerCase().includes(search)
    );
});

// Get selected customer name
const selectedCustomerName = computed(() => {
    const customer = customers.value.find(c => c.id === props.modelValue);
    return customer ? customer.businessName : '';
});

// Watch for changes in modelValue to update searchTerm
watch(() => props.modelValue, (newValue) => {
    const customer = customers.value.find(c => c.id === newValue);
    searchTerm.value = customer ? customer.businessName : '';
});
</script>

<template>
    <div class="d-flex align-center">
        <v-autocomplete
            density="compact"
            :model-value="props.modelValue"
            @update:model-value="handleCustomerChange"
            :items="filteredCustomers"
            item-title="businessName"
            item-value="id"
            :error-messages="error"
            :label="t('page.sales_request_form.customer_details.businessName')"
            class="flex-grow-1"
            v-model:search-input="searchTerm"
            hide-no-data
            hide-selected
            clearable
        >
            <template v-slot:no-data>
                <v-list-item>
                    <v-list-item-title>
                        No customers found. 
                        <v-btn
                            variant="text"
                            color="primary"
                            @click="showDialog = true"
                        >
                            Add new customer
                        </v-btn>
                    </v-list-item-title>
                </v-list-item>
            </template>
        </v-autocomplete>
        
        <v-btn
            icon
            variant="text"
            color="primary"
            class="ml-2 align-self-center"
            style="margin-top: -20px;"
            @click="showDialog = true"
        >
            <v-icon>add</v-icon>
        </v-btn>
        
        <!-- New Customer Dialog -->
        <v-dialog v-model="showDialog" max-width="600px">
            <v-card>
                <v-card-title>
                    <span class="text-h5">Add New Customer</span>
                </v-card-title>
                
                <v-card-text>
                    <v-container>
                        <v-row>
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newCustomer.businessName"
                                    :error-messages="formErrors.businessName"
                                    label="Business Name *"
                                    required
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newCustomer.legalName"
                                    label="Legal Name"
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newCustomer.salesforceOppId"
                                    label="Salesforce Opportunity ID"
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-select
                                    v-model="newCustomer.customerRelationship"
                                    :items="customerRelationshipOptions"
                                    :error-messages="formErrors.customerRelationship"
                                    label="Customer Relationship *"
                                    required
                                    density="compact"
                                ></v-select>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newCustomer.customerWebsite"
                                    :error-messages="formErrors.customerWebsite"
                                    label="Customer Website *"
                                    required
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                        </v-row>
                    </v-container>
                    <small>* indicates required field</small>
                </v-card-text>
                
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="blue-darken-2" variant="plain" @click="showDialog = false">
                        Cancel
                    </v-btn>
                    <v-btn color="blue-darken-1" variant="tonal" @click="addCustomer">
                        Save
                    </v-btn>
                    <v-btn color="blue-darken-1" variant="outlined" @click="addCustomer">
                        Save & Select
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

