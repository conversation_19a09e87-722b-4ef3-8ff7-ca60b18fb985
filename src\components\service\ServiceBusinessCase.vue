<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';

const props = defineProps({
  modelValue: { // Corresponds to formData.service_business_case.justification
    type: String,
    required: true,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const requiredRule = (value: any) => !!value || 'This field is required.';

const localJustification = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

</script>

<template>
  <v-card class="mb-4">
    <v-card-title>Service Business Case <span style="color:red">*</span></v-card-title>
    <v-card-text>
      <v-textarea
        v-model="localJustification"
        label="Include Detailed Justification For Service Discount Request and Detail Service Penalties"
        rows="6"
        auto-grow
        variant="outlined"
        :rules="[requiredRule]"
      />
    </v-card-text>
  </v-card>
</template>

<style scoped>
/* Component-specific styles */
</style>
