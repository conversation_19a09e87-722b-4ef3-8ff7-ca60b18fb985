<script setup lang="ts">
/**
 * @file Worksheets tab component for Sales Request Form.
 * @version 1.0.0
 * @since 1.0.0
 */

import type { WorksheetProduct } from '@/lib/common/types';
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import ProductCategoryCondensedView from './ProductCategoryCondensedView.vue';
import ProductCategoryExpandedView from './ProductCategoryExpandedView.vue';

// Add language support
const { t } = useI18n();

// Tab state
const currentTab = ref('all'); // 'all', 'hardware', 'solutions'

// Product options with details
const productDetails = {
  // Hardware products
  'HW1': { itemNumber: 'HW-001', msrp: 1000 },
  'HW2': { itemNumber: 'HW-002', msrp: 1500 },
  'HW3': { itemNumber: 'HW-003', msrp: 2000 },
  // Software products
  'SW1': { itemNumber: 'SW-001', msrp: 500 },
  'SW2': { itemNumber: 'SW-002', msrp: 750 },
  'SW3': { itemNumber: 'SW-003', msrp: 1000 },
  // Accessories
  '11_22': { itemNumber: 'ACC-001', msrp: 100 },
  '11_33': { itemNumber: 'ACC-002', msrp: 150 },
  '22_11': { itemNumber: 'ACC-003', msrp: 200 },
  '22_33': { itemNumber: 'ACC-004', msrp: 250 },
  '33_11': { itemNumber: 'ACC-005', msrp: 300 },
  '33_22': { itemNumber: 'ACC-006', msrp: 350 }
};

// Product options for dropdown
const hardwareOptions = [
  { title: 'Hardware 1', value: 'HW1' },
  { title: 'Hardware 2', value: 'HW2' },
  { title: 'Hardware 3', value: 'HW3' }
];

const softwareOptions = [
  { title: 'Solution Category 1', value: 'SW1' },
  { title: 'Solution Category 2', value: 'SW2' },
  { title: 'Solution Category 3', value: 'SW3' }
];

// Separate form data for hardware and software
const hardwareProducts = ref<ProductItem[]>([createEmptyProduct()]);
const softwareProducts = ref<ProductItem[]>([]);

// Sub-product options based on parent product
const getSubProductOptions = (parentValue: string) => {
  const options = {
    'HW1': [
      { title: 'Accessory 1-1', value: '11_22' },
      { title: 'Accessory 1-2', value: '11_33' }
    ],
    'HW2': [
      { title: 'Accessory 2-1', value: '22_11' },
      { title: 'Accessory 2-2', value: '22_33' }
    ],
    'HW3': [
      { title: 'Accessory 3-1', value: '33_11' },
      { title: 'Accessory 3-2', value: '33_22' }
    ],
    'SW1': [
      { title: 'Solution Item 1-1', value: '11_22' },
      { title: 'Solution Item 1-2', value: '11_33' }
    ],
    'SW2': [
      { title: 'Solution Item 2-1', value: '22_11' },
      { title: 'Solution Item 2-2', value: '22_33' }
    ],
    'SW3': [
      { title: 'Solution Item 3-1', value: '33_11' },
      { title: 'Solution Item 3-2', value: '33_22' }
    ]
  };
  return options[parentValue as keyof typeof options] || [];
};

// Define the type for a product item
interface ProductField {
  type: string; // e.g., 'dropdown', 'number', 'text'
  value: string | number;
  readonly?: boolean;
  min?: number;
  max?: number;
}

interface ProductItem {
  productName: ProductField;       // Main product or sub-product name/selection
  dsdQuantity: ProductField;       // DSD Quantity
  dealerITsQuantity: ProductField; // Dealer ITs Quantity
  itemNumber: ProductField;        // Item Number (often readonly, derived)
  requestSellingPrice: ProductField; // Requested Selling Price (often readonly, calculated)
  msrp: ProductField;              // Manufacturer's Suggested Retail Price (readonly, derived)
  percentOfMsrp: ProductField;     // Percentage of MSRP (user input, affects selling price)
  subProducts: ProductItem[];      // Array of accessories or sub-components
}



// Helper function to calculate total quantity (DSD + Dealer ITs) for a product or sub-product
const calculateTotalQuantity = (product: ProductItem): number => {
  const dsdQty = Number(product.dsdQuantity.value) || 0;
  const dealerQty = Number(product.dealerITsQuantity.value) || 0;
  return dsdQty + dealerQty;
};

// Helper function to calculate sum for a list of products (either selling price or MSRP)
const calculateSumForProductList = (products: ProductItem[], priceField: 'requestSellingPrice' | 'msrp'): number => {
  const calculateProductTotal = (product: ProductItem): number => {
    const productQty = calculateTotalQuantity(product);
    const productPrice = Number(product[priceField].value) || 0;
    const productTotal = productQty * productPrice;

    const subProductsTotal = product.subProducts.reduce((sum: number, sub: ProductItem) => {
      const subQty = calculateTotalQuantity(sub);
      const subPrice = Number(sub[priceField].value) || 0;
      return sum + (subQty * subPrice);
    }, 0);

    return productTotal + subProductsTotal;
  };
  return products.reduce((sum, p) => sum + calculateProductTotal(p), 0);
};

// Hardware Summary Calculations
const hardwareTotalSellingPrice = computed(() => {
  return calculateSumForProductList(hardwareProducts.value, 'requestSellingPrice');
});

const hardwareTotalMSRP = computed(() => {
  return calculateSumForProductList(hardwareProducts.value, 'msrp');
});

const hardwareMsrpPercentage = computed(() => {
  if (hardwareTotalMSRP.value === 0) return 0;
  return (hardwareTotalSellingPrice.value / hardwareTotalMSRP.value) * 100;
});

// Software Summary Calculations
const softwareTotalSellingPrice = computed(() => {
  return calculateSumForProductList(softwareProducts.value, 'requestSellingPrice');
});

const softwareTotalMSRP = computed(() => {
  return calculateSumForProductList(softwareProducts.value, 'msrp');
});

const softwareMsrpPercentage = computed(() => {
  if (softwareTotalMSRP.value === 0) return 0;
  return (softwareTotalSellingPrice.value / softwareTotalMSRP.value) * 100;
});

// Overall Summary calculations
const totalItems = computed(() => {
  const hardwareCount = hardwareProducts.value.length + 
    hardwareProducts.value.reduce((sum, p) => sum + p.subProducts.length, 0);
  const softwareCount = softwareProducts.value.length + 
    softwareProducts.value.reduce((sum, p) => sum + p.subProducts.length, 0);
  return hardwareCount + softwareCount;
});

const totalSellingPrice = computed(() => {
  return hardwareTotalSellingPrice.value + softwareTotalSellingPrice.value;
});

const totalMSRP = computed(() => {
  return hardwareTotalMSRP.value + softwareTotalMSRP.value;
});

const msrpPercentage = computed(() => {
  if (totalMSRP.value === 0) return 0;
  return (totalSellingPrice.value / totalMSRP.value) * 100;
});

// Function to create an empty product
function createEmptyProduct(): ProductItem {
  return {
    productName: { type: 'dropdown', value: '' },
    dsdQuantity: { type: 'number', value: 0 },
    dealerITsQuantity: { type: 'number', value: 0 },
    itemNumber: { type: 'text', value: '', readonly: true },
    requestSellingPrice: { type: 'number', value: 0, readonly: true },
    msrp: { type: 'number', value: 0, readonly: true },
    percentOfMsrp: { type: 'number', value: 100, min: 1, max: 100 },
    proposalType: { type: 'radio', value: 'primary' }, // Added for hardware proposal type
    subProducts: []
  };
}

// Function to create an empty sub-product
function createEmptySubProduct(): ProductItem {
  return {
    productName: { type: 'dropdown', value: '' },
    dsdQuantity: { type: 'number', value: 0 },
    dealerITsQuantity: { type: 'number', value: 0 },
    itemNumber: { type: 'text', value: '', readonly: true },
    requestSellingPrice: { type: 'number', value: 0, readonly: true },
    msrp: { type: 'number', value: 0, readonly: true },
    percentOfMsrp: { type: 'number', value: 100, min: 1, max: 100 },
    subProducts: []
  };
}

// Add a new product
const addProduct = (type: 'hardware' | 'software') => {
  if (type === 'hardware') {
    hardwareProducts.value.push(createEmptyProduct());
  } else {
    softwareProducts.value.push(createEmptyProduct());
  }
};

// Remove a product
const removeProduct = (type: 'hardware' | 'software', index: number) => {
  if (type === 'hardware') {
    hardwareProducts.value.splice(index, 1);
  } else {
    softwareProducts.value.splice(index, 1);
  }
};

// Add a sub-product
const addSubProduct = (type: 'hardware' | 'software', productIndex: number) => {
  const products = type === 'hardware' ? hardwareProducts : softwareProducts;
  products.value[productIndex].subProducts.push(createEmptySubProduct());
};

// Remove a sub-product
const removeSubProduct = (type: 'hardware' | 'software', productIndex: number, subProductIndex: number) => {
  const products = type === 'hardware' ? hardwareProducts : softwareProducts;
  products.value[productIndex].subProducts.splice(subProductIndex, 1);
};

// Update product details when product is selected
const updateProductDetails = (product: any, productValue: string, isSubProduct = false) => {
  if (!productValue) return;
  
  const details = productDetails[productValue as keyof typeof productDetails];
  if (details) {
    product.itemNumber.value = details.itemNumber;
    product.msrp.value = details.msrp;
    // Calculate selling price based on MSRP and percentage
    updateSellingPrice(product);
  }
};

// Update selling price based on MSRP and percentage
const updateSellingPrice = (product: any) => {
  const msrp = Number(product.msrp.value);
  const percent = Number(product.percentOfMsrp.value) || 0;
  
  // Ensure percentage is between 1 and 100
  if (percent < 1) product.percentOfMsrp.value = 1;
  if (percent > 100) product.percentOfMsrp.value = 100;
  
  // Calculate selling price (MSRP * (percent/100))
  product.requestSellingPrice.value = (msrp * (percent / 100)).toFixed(2);
};

// Handle percentage change
const handlePercentChange = (product: any) => {
  updateSellingPrice(product);
};

// Calculate summary for a single product and its sub-products (for expanded view prop)
const calculateProductSummaryForExpandedView = (product: ProductItem): { totalMsrp: number; totalSellingPrice: number; avgPercent: string | number } => {
  let mainProductMsrp = 0;
  let mainProductSellingPrice = 0;
  const mainProductQty = calculateTotalQuantity(product);

  if (product.msrp && product.msrp.value) {
    mainProductMsrp = mainProductQty * Number(product.msrp.value);
  }
  if (product.requestSellingPrice && product.requestSellingPrice.value) {
    mainProductSellingPrice = mainProductQty * Number(product.requestSellingPrice.value);
  }

  const subProductsTotalMsrp = product.subProducts.reduce((sum, sub) => {
    const subQty = calculateTotalQuantity(sub);
    return sum + (subQty * (Number(sub.msrp.value) || 0));
  }, 0);

  const subProductsTotalSellingPrice = product.subProducts.reduce((sum, sub) => {
    const subQty = calculateTotalQuantity(sub);
    return sum + (subQty * (Number(sub.requestSellingPrice.value) || 0));
  }, 0);

  const totalMsrpValue = mainProductMsrp + subProductsTotalMsrp;
  const totalSellingPriceValue = mainProductSellingPrice + subProductsTotalSellingPrice;
  
  let avgPercent: string | number = '0.00'; // Default to 0.00 if no prices
  if (totalMsrpValue > 0) {
    avgPercent = ((totalSellingPriceValue / totalMsrpValue) * 100).toFixed(2);
  } else if (totalSellingPriceValue > 0) {
    avgPercent = 'N/A'; // MSRP is 0 but selling price is not
  }

  return {
    totalMsrp: totalMsrpValue,
    totalSellingPrice: totalSellingPriceValue,
    avgPercent,
  };
};

const setFormData = (requestItems: any[]) => {
    if (!requestItems || requestItems.length === 0) {
        hardwareProducts.value = [createEmptyProduct()];
        softwareProducts.value = [];
        return;
    }

    const newHardware: ProductItem[] = [];
    const newSoftware: ProductItem[] = [];

    // Create a reverse map from itemNumber to productKey
    const itemNumberToProductKey = Object.entries(productDetails).reduce((acc, [key, value]) => {
        acc[value.itemNumber] = key;
        return acc;
    }, {} as Record<string, string>);

    requestItems.forEach(item => {
        const product = createEmptyProduct();

        // Populate fields from backend data
        product.itemNumber.value = item.itemId;
        product.msrp.value = item.msrp;
        product.requestSellingPrice.value = item.requestedSellingPrice;
        product.dsdQuantity.value = item.dsdQuantity;
        product.dealerITsQuantity.value = item.dealerIt; // Note the key name difference
        product.productName.value = itemNumberToProductKey[item.itemId] || '';

        // Calculate percent of MSRP
        if (item.msrp > 0) {
            product.percentOfMsrp.value = (item.requestedSellingPrice / item.msrp) * 100;
        } else {
            product.percentOfMsrp.value = 100;
        }

        // Categorize into hardware or software
        if (item.itemId?.startsWith('HW')) {
            newHardware.push(product);
        } else if (item.itemId?.startsWith('SW')) {
            newSoftware.push(product);
        }
    });

    hardwareProducts.value = newHardware.length > 0 ? newHardware : [createEmptyProduct()];
    softwareProducts.value = newSoftware;
};

// Expose methods for parent component
defineExpose({
    getFormData: () => {
        const allProducts = [...hardwareProducts.value, ...softwareProducts.value];

        const flattenProducts = (products: ProductItem[]) => {
            let items: any[] = [];
            for (const p of products) {
                if (!p.itemNumber.value) continue; // Skip empty/unselected products

                items.push({
                    itemId: p.itemNumber.value,
                    dsdQuantity: p.dsdQuantity.value,
                    dealerIt: p.dealerITsQuantity.value,
                    msrp: p.msrp.value,
                    requestedSellingPrice: p.requestSellingPrice.value
                });

                if (p.subProducts && p.subProducts.length > 0) {
                    items = items.concat(flattenProducts(p.subProducts));
                }
            }
            return items;
        };

        return flattenProducts(allProducts);
    },
    setFormData
});
</script>

<template>
  <v-container fluid>
    <v-card class="mb-4">
      <v-card-title class="text-h6 bg-grey-lighten-3 d-flex justify-space-between align-center">
        <span>{{ t('page.sales_request_form.worksheets.title') }}</span>
      </v-card-title>
      <v-tabs v-model="currentTab" grow class="mb-0">
        <v-tab value="all">{{ t('page.sales_request_form.worksheets.tabs.all') }}</v-tab>
        <v-tab value="hardware">{{ t('page.sales_request_form.worksheets.tabs.hardware') }}</v-tab>
        <v-tab value="solutions">{{ t('page.sales_request_form.worksheets.tabs.solutions') }}</v-tab>
      </v-tabs>
      <v-divider></v-divider>
      <v-card-text>
        <v-window v-model="currentTab">
          <v-window-item value="all">
            <div class="mb-6">
              <div class="text-h6 mb-3">{{ t('page.sales_request_form.worksheets.total_summary_title') }}</div>
              <v-row dense>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_selling_price') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ totalSellingPrice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ totalMSRP.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_percent_of_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">{{ msrpPercentage.toFixed(2) }}%</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
            <ProductCategoryCondensedView
              :title="t('page.sales_request_form.worksheets.hardware_title')"
              :products="hardwareProducts"
              productType="hardware"
              :productOptions="hardwareOptions"
              :getSubProductOptionsFunc="getSubProductOptions"
              :updateProductDetailsFunc="updateProductDetails"
              :handlePercentChangeFunc="handlePercentChange"
              :removeProductFunc="removeProduct"
              :addSubProductFunc="addSubProduct"
              :removeSubProductFunc="removeSubProduct"
              :addProductToListFunc="addProduct"
              :t="t"
            />
            <ProductCategoryCondensedView
              class="mt-6"
              :title="t('page.sales_request_form.worksheets.solutions_title_condensed')"
              :products="softwareProducts"
              productType="software"
              :productOptions="softwareOptions"
              :getSubProductOptionsFunc="getSubProductOptions"
              :updateProductDetailsFunc="updateProductDetails"
              :handlePercentChangeFunc="handlePercentChange"
              :removeProductFunc="removeProduct"
              :addSubProductFunc="addSubProduct"
              :removeSubProductFunc="removeSubProduct"
              :addProductToListFunc="addProduct"
              :t="t"
            />
          </v-window-item>

          <v-window-item value="hardware">
            <div class="mb-6">
              <div class="text-h6 mb-3">{{ t('page.sales_request_form.worksheets.hardware_summary_title') }}</div>
              <v-row dense>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_selling_price') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ hardwareTotalSellingPrice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ hardwareTotalMSRP.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_percent_of_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">{{ hardwareMsrpPercentage.toFixed(2) }}%</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
            <ProductCategoryExpandedView
              :title="t('page.sales_request_form.worksheets.hardware_title')"
              :products="hardwareProducts"
              productType="hardware"
              :productOptions="hardwareOptions"
              :getSubProductOptionsFunc="getSubProductOptions"
              :updateProductDetailsFunc="updateProductDetails"
              :handlePercentChangeFunc="handlePercentChange"
              :removeProductFunc="removeProduct"
              :addSubProductFunc="addSubProduct"
              :removeSubProductFunc="removeSubProduct"
              :addProductToListFunc="addProduct"
              :calculateProductSummaryFunc="calculateProductSummaryForExpandedView"
              :t="t"
            />
          </v-window-item>

          <v-window-item value="solutions">
            <div class="mb-6">
              <div class="text-h6 mb-3">{{ t('page.sales_request_form.worksheets.solutions_summary_title') }}</div>
              <v-row dense>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_selling_price') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ softwareTotalSellingPrice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_total_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">${{ softwareTotalMSRP.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" md="4">
                  <v-card variant="outlined" class="pa-3 fill-height d-flex flex-column justify-center">
                    <div class="text-caption text-grey">{{ t('page.sales_request_form.worksheets.summary_percent_of_msrp') }}</div>
                    <div class="text-h5 font-weight-bold mt-1">{{ softwareMsrpPercentage.toFixed(2) }}%</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
            <ProductCategoryExpandedView
              :title="t('page.sales_request_form.worksheets.solutions_title_expanded')"
              :products="softwareProducts"
              productType="software"
              :productOptions="softwareOptions"
              :getSubProductOptionsFunc="getSubProductOptions"
              :updateProductDetailsFunc="updateProductDetails"
              :handlePercentChangeFunc="handlePercentChange"
              :removeProductFunc="removeProduct"
              :addSubProductFunc="addSubProduct"
              :removeSubProductFunc="removeSubProduct"
              :addProductToListFunc="addProduct"
              :calculateProductSummaryFunc="calculateProductSummaryForExpandedView"
              :t="t"
            />
             <div v-if="softwareProducts.length === 0" class="text-center mt-6">
                <v-btn color="primary" @click="addProduct('software')">
                  {{ t('page.sales_request_form.worksheets.buttons.add_main_unit_typed', { unit_type: t('page.sales_request_form.worksheets.solutions_title_expanded') }) }}
                </v-btn>
            </div>
          </v-window-item>
        </v-window>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<style scoped>
  /* ... (styles remain the same) */
.product-container {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 16px;
}

.product-row {
  border-bottom: 1px dashed #e0e0e0;
}

.sub-products {
  padding-left: 16px;
  border-left: 2px solid #2196F3;
}

.sub-product-row {
  margin-top: 8px;
}
</style>
