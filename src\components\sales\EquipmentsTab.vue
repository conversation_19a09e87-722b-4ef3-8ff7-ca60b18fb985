<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Equipment section data
const sellingPrice = ref(0);
const msrp = ref(0);
const percentOfMsrp = computed(() => {
  if (!msrp.value) return 'N/A';
  return ((sellingPrice.value / msrp.value) * 100).toFixed(2) + '%';
});

// Potential Competition multi-select
const competitionOptions = [
  { title: 'None', value: 'none' },
  { title: 'Xerox', value: 'xerox' },
  { title: 'Ricoh', value: 'ricoh' },
  { title: 'HP', value: 'hp' },
  { title: 'Konica M', value: 'konica' },
  { title: 'Sharp', value: 'sharp' }
];
const selectedCompetition = ref([]);

// MFP Incumbent - will be calculated later
const mfpIncumbent = ref('');
const incumbentOptions = [
  { title: 'Net New', value: 'net_new' },
  { title: 'Canon BSS', value: 'canon_bss' },
  { title: 'Canon Dealer', value: 'canon_dealer' },
  { title: 'HP', value: 'hp' },
  { title: 'Konica M', value: 'konica_m' },
  { title: 'Kyocera', value: 'kyocera' },
  { title: 'Ricoh', value: 'ricoh' },
  { title: 'Toshiba', value: 'toshiba' },
  { title: 'Xerox', value: 'xerox' },
  { title: 'Other', value: 'other' }
];

// Add a computed property to check if the table should be shown
const showTableSection = computed(() => {
  return mfpIncumbent.value !== 'net_new';
});

// New fields for installation and strategy section
const potentialUnits = ref('');
const installationDate = ref('');
const specialConsiderations = ref('');
const hardwarePricingStrategy = ref('');
const softwareSalesStrategy = ref('');

// Table data for the bottom section
const vendors = ['Canon BSS', 'Canon Dealer', 'Xerox', 'Ricoh', 'Sharp', 'Konica M.'];
const categories = ['B&W', 'Color', 'Desktop Printer'];
const tableData = ref(
  categories.map(category => {
    return {
      category,
      units: '0',
      values: vendors.map(() => '0'),
      isValid: true,
      errorMessage: ''
    };
  })
);

// Validate row totals
const validateRow = (rowIndex: number) => {
  const row = tableData.value[rowIndex];
  
  // Check if units is positive
  if (parseInt(row.units) <= 0) {
    row.isValid = false;
    row.errorMessage = 'Number of units must be greater than 0';
    return;
  }
  
  // Calculate sum of percentages
  const sum = row.values.reduce((total, value) => total + parseInt(value || '0'), 0);
  
  // Check if sum equals 100%
  if (sum !== 100) {
    row.isValid = false;
    row.errorMessage = `Total percentage must equal 100%. Current total: ${sum}%`;
    return;
  }
  
  row.isValid = true;
  row.errorMessage = '';
};

// Handle number-only input for percentage fields
const onNumberInput = (event: Event, rowIndex: number, colIndex: number) => {
  const target = event.target as HTMLInputElement;
  const value = target.value.replace(/[^0-9]/g, '');
  tableData.value[rowIndex].values[colIndex] = value;
  
  // Validate row after input
  validateRow(rowIndex);
};

// Handle number-only input for units field
const onUnitNumberInput = (event: Event, rowIndex: number) => {
  const target = event.target as HTMLInputElement;
  const value = target.value.replace(/[^0-9]/g, '');
  tableData.value[rowIndex].units = value;
  
  // Validate row after input
  validateRow(rowIndex);
};

// Format date to MM/DD/YYYY
const formatDate = (event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value.replace(/[^0-9]/g, '');
  
  if (value.length > 8) {
    value = value.slice(0, 8);
  }
  
  if (value.length >= 4) {
    value = value.slice(0, 2) + '/' + value.slice(2);
  }
  
  if (value.length >= 7) {
    value = value.slice(0, 5) + '/' + value.slice(5);
  }
  
  installationDate.value = value;
};

// Define emits for form data
const emit = defineEmits(['update:formData']);

const setFormData = (data: any) => {
    if (!data) return;

    // Populate simple fields
    potentialUnits.value = data.potentialUnitCount || '';
    installationDate.value = data.installationDate || '';
    specialConsiderations.value = data.specialConsideration || '';
    hardwarePricingStrategy.value = data.hardwarePricingStrategy || '';
    softwareSalesStrategy.value = data.softwareSalesStrategy || '';

    // Populate the MFP Incumbents table from the flat list
    if (data.mfpIncumbents && Array.isArray(data.mfpIncumbents)) {
        // Reset existing table data
        tableData.value.forEach(row => {
            row.units = '0';
            row.values.fill('0');
        });

        data.mfpIncumbents.forEach((incumbent: any) => {
            const rowIndex = tableData.value.findIndex(row => row.category === incumbent.competitionType);
            const colIndex = vendors.findIndex(vendor => vendor === incumbent.currentIncumbent);

            if (rowIndex !== -1 && colIndex !== -1) {
                tableData.value[rowIndex].units = incumbent.noOfUnit.toString();
                tableData.value[rowIndex].values[colIndex] = incumbent.incumbentPercent.toString();
            }
        });
    }
};

// Expose methods for parent component
defineExpose({
  getFormData: () => {
    // Transform tableData into the flat list of mfpIncumbents the backend expects
    const mfpIncumbents = tableData.value.flatMap(row => {
        if (parseInt(row.units, 10) === 0) {
            return []; // Skip rows with no units
        }
        return row.values.map((percent, index) => ({
            noOfUnit: parseInt(row.units, 10),
            currentIncumbent: vendors[index],
            competitionName: selectedCompetition.value.join(', '),
            competitionType: row.category,
            incumbentPercent: parseInt(percent, 10) || 0
        })).filter(item => item.incumbentPercent > 0); // Only include items with a percentage
    });

    return {
      potentialUnitCount: potentialUnits.value,
      installationDate: installationDate.value,
      specialConsideration: specialConsiderations.value,
      hardwarePricingStrategy: hardwarePricingStrategy.value,
      softwareSalesStrategy: softwareSalesStrategy.value,
      mfpIncumbents: mfpIncumbents
    };
  },
  setFormData
});

// Add validation refs
const form = ref(null);
const rules = {
  required: value => !!value || 'This field is required',
  competitionRequired: value => (value && value.length > 0) || 'At least one option must be selected',
  tableRequired: {
    validate: () => {
      if (mfpIncumbent.value === 'net_new') return true;
      return tableData.value.every(row => {
        const hasUnits = parseInt(row.units) > 0;
        const hasValidPercentages = row.values.every(val => {
          const num = parseInt(val);
          return !isNaN(num) && num >= 0 && num <= 100;
        });
        return !hasUnits || (hasUnits && hasValidPercentages);
      });
    },
    message: 'Please fill all required fields in the table'
  }
};

// Update the form validation method
const validate = () => {
  const baseValid = form.value?.validate();
  const tableValid = rules.tableRequired.validate();
  return baseValid && (mfpIncumbent.value === 'net_new' || tableValid);
};
</script>

<template>
  <v-container fluid>
    <v-row>
      <!-- Equipment Section -->
      <!-- <v-col cols="12">
        <v-card>
          <v-card-title class="text-h6 bg-grey-lighten-3">
            Equipment
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="4">
                <v-text-field
                  v-model.number="sellingPrice"
                  label="Selling Price"
                  prefix="$"
                  type="number"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="4">
                <v-text-field
                  v-model.number="msrp"
                  label="MSRP"
                  prefix="$"
                  type="number"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="4">
                <v-text-field
                  :model-value="percentOfMsrp"
                  label="% of MSRP"
                  readonly
                  density="compact"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col> -->



      <!-- Potential Competition Section -->
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title class="text-h6 bg-grey-lighten-3">
            Potential Competition *
          </v-card-title>
          <v-card-text>
            <v-select
              v-model="selectedCompetition"
              :items="competitionOptions"
              label="PLEASE CHECK ALL THAT APPLY"
              :rules="[rules.competitionRequired]"
              multiple
              chips
              density="compact"
              required
            ></v-select>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- MFP Incumbent Section -->
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title class="text-h6 bg-grey-lighten-3">
            MFP Incumbent *
          </v-card-title>
          <v-card-text>
            <v-select
              v-model="mfpIncumbent"
              :items="incumbentOptions"
              label="Current Incumbent"
              :rules="[rules.required]"
              density="compact"
              required
            ></v-select>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Table Section -->
      <v-col v-if="showTableSection" cols="12">
        <v-card>
          <v-card-title class="text-subtitle-1 bg-yellow-lighten-3 text-center">
            This Section MUST be Completed For Your Request to Be Reviewed
          </v-card-title>
          <v-card-text>
            <v-table density="compact" class="bordered-table">
              <thead>
                <tr>
                  <th class="border-right"></th>
                  <th class="border-right">No. of Units</th>
                  <th v-for="vendor in vendors" :key="vendor" class="text-center border-right">
                    {{ vendor }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(row, index) in tableData" :key="index" :class="{'error-row': !row.isValid}">
                  <td class="border-right">{{ row.category }}</td>
                  <td class="border-right">
                    <v-text-field
                      v-model="tableData[index].units"
                      density="compact"
                      hide-details
                      class="text-center"
                      variant="plain"
                      type="text"
                      @input="onUnitNumberInput($event, index)"
                      :error="!row.isValid && parseInt(row.units) <= 0"
                      :rules="[v => v !== '' || 'Required', v => !isNaN(parseInt(v)) || 'Must be a number']"
                    ></v-text-field>
                  </td>
                  <td v-for="(value, vIndex) in row.values" :key="vIndex" class="text-center border-right">
                    <v-tooltip
                      :disabled="row.isValid"
                      location="top"
                      :text="row.errorMessage"
                    >
                      <template v-slot:activator="{ props }">
                        <v-text-field
                          v-bind="props"
                          v-model="tableData[index].values[vIndex]"
                          density="compact"
                          hide-details
                          class="text-center"
                          variant="plain"
                          suffix="%"
                          type="text"
                          @input="onNumberInput($event, index, vIndex)"
                          :disabled="parseInt(row.units) <= 0"
                          :error="!row.isValid && parseInt(row.units) > 0"
                          :rules="[v => {
                            if (parseInt(row.units) <= 0) return true;
                            const num = parseInt(v);
                            if (isNaN(num)) return 'Must be a number';
                            if (num < 0 || num > 100) return 'Must be between 0-100';
                            return true;
                          }]"
                        ></v-text-field>
                      </template>
                    </v-tooltip>
                  </td>
                </tr>
              </tbody>
            </v-table>
            <v-alert
              type="info"
              density="compact"
              class="mt-2"
              variant="tonal"
            >
              *Only Select Canon Dealer if the dealer sold the customer the unit. Does not include inter-territorals. Please specify Dealer name in strategy.
            </v-alert>
          </v-card-text>
        </v-card>
      </v-col>
            <!-- Installation and Units Section -->
      <v-col cols="12">
        <v-card>
          <v-card-title class="text-h6 bg-grey-lighten-3">
            Installation Details
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="potentialUnits"
                  label="# of Potential Units"
                  type="number"
                  density="compact"
                  hint="Please Enter The # of Potential Unit. If this differs from total on form, identify options below."
                  persistent-hint
                  class="mb-2"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="installationDate"
                  label="Installation Date"
                  type="date"
                  placeholder="MM/DD/YYYY"
                  density="compact"
                  hint="PLEASE ENTER THE INSTALLATION DATE"
                  persistent-hint
                  class="mb-2"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Strategy Section -->
      <v-col cols="12">
        <v-card>
          <v-card-title class="text-h6 bg-grey-lighten-3">
            Strategy Information
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="specialConsiderations"
                  label="Special Considerations/Options"
                  auto-grow
                  rows="3"
                  density="compact"
                  bg-color="light-green-lighten-5"
                  class="mb-3"
                ></v-textarea>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="6">
                <v-textarea
                  v-model="hardwarePricingStrategy"
                  label="Hardware Pricing Strategy"
                  auto-grow
                  rows="4"
                  density="compact"
                  bg-color="light-green-lighten-5"
                ></v-textarea>
              </v-col>
              <v-col cols="12" md="6">
                <v-textarea
                  v-model="softwareSalesStrategy"
                  label="Software Sales Strategy"
                  auto-grow
                  rows="4"
                  density="compact"
                  bg-color="light-green-lighten-5"
                ></v-textarea>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
.bordered-table {
  border-collapse: collapse;
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.border-right {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

/* Override Vuetify's default table styles */
:deep(.v-table) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.v-table th), :deep(.v-table td) {
  padding: 0 8px;
}
</style>
