/**
 * @file Pinia store defining user state for the application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { defineStore } from 'pinia';
import type { UserStoreState } from '@/lib/common/types';
import type { MSGraphUser } from '@/lib/common/types';
import type { PostLoginResponse } from '@/lib/api';

/**
 * ------
 * Export
 * ------
 */

export const useInternalUserStore = defineStore( 'InternalUserStore',
{
    /**
	 * Store state variables.
	 */
    state : () : PostLoginResponse =>
    {
        return {
            userId: null,
            microsoftAccountId: null,
            displayName: null,
            email: null,
            canonId: null,
            userStatus: null,
            language: null,
            theme: null,
            roles:null,
            createdAt: null,
            createdBy: null,
            updatedAt: null,
            updatedBy: null,
            approver: true
        }
    },

    /**
	 * Store actions (same as methods).
	 */
    actions :
    {
        /**
         * Sets the user store state variables given the properties passed.
         * 
         * @param { any } [userDetails] - Login Refresh returned data value (POST).
         */
        setInternalUser(user: PostLoginResponse) {
            this.userId = user.userId;
            this.microsoftAccountId = user.microsoftAccountId;
            this.displayName = user.displayName;
            this.email = user.email;
            this.canonId = user.canonId;
            this.userStatus = user.userStatus;
            this.language = user.language;
            this.theme = user.theme;
            this.roles = user.roles;
            this.createdAt = user.createdAt;
            this.createdBy = user.createdBy;
            this.updatedAt = user.updatedAt;
            this.updatedBy = user.updatedBy;
            this.approver = user.approver;
        },
        


        /**
         * Clears user data from the store.
         */
        clearInternalUser() {
            this.userId = null;
            this.microsoftAccountId = null;
            this.displayName = null;
            this.email = null;
            this.canonId = null;
            this.userStatus = null;
            this.language = null;
            this.theme = null;
            this.roles = null;
            this.createdAt = null;
            this.createdBy = null;
            this.updatedAt = null;
            this.updatedBy = null;
            this.approver = null;
        }
        
    }
}); 