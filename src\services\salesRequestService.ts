import axios from 'axios';
import { useAppStore } from '@/stores/AppStore';
import getAccessToken from '@/composables/auth/getAccessToken.js';

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add a request interceptor to include the auth token from the app store
apiClient.interceptors.request.use(
  (config) => {
    const appStore = useAppStore();
    const token = appStore.authenticationToken;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export const saveSalesRequest = async (data: any) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  return apiClient.post('/dsd/request/save', data, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

export const getSalesRequest = (requestId: number) => {
  return apiClient.get(`/dsd/request/${requestId}`);
};
