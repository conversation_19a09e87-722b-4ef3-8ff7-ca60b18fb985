/**
 * @file Type definitions for the Vite environment variables located in the .env.* files.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2022 Canon Canada Inc.
 */

/// <reference types="vite/client" />

// Vue module declaration for TypeScript
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Type definitions for Environmental variables.
interface ImportMetaEnv
{
	/**
     * Flag indicating if application is running in development mode or not (TRUE or FALSE).
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_DEV: string;

	/**
     * Title of the application used in header, footer and browser title.
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_TITLE: string;

	/**
     * Version of the applications shown in footer.
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_VERSION: string;

	/**
     * Application icon used in header. Should be a valid Google Material Designs Icon.
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_ICON: string;

	/**
     * Enables or disables the theme controlling function in the UI (TRUE or FALSE);
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_CONTROL_THEME: string;

    /**
     * Microsoft Entra ID (Azure) client application ID. Provided by Azure administrators.
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_AUTH_CLIENT_ID: string;

    /**
     * Microsoft Entra ID (Azure) CUSA tenant ID. Provided by Azure administrators.
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_AUTH_TENANT_ID: string;

    /**
     * Microsoft Entra ID (Azure) app redirection URL. Provided by Azure administrators. Must be registered in Azure.
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_AUTH_REDIRECT_URL: string;

    /**
     * Full scope name of exposed permission scope that allows general access to backend.
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_API_SCOPE: string;

    /**
     * Base URL of the local app API.
     *
     * @type {string}
     * @memberof ImportMetaEnv
     */
    readonly VITE_APP_API_BASE_URL: string;
}

interface ImportMeta
{
	readonly env : ImportMetaEnv;
}