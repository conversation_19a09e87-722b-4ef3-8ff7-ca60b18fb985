<template>
  <service-approval-table
    :term-length="termLength"
    :service-value-pack="serviceValuePack"
    :service-levels="serviceLevels"
    :sections-data="tableSections"
    @update:term-length="termLength = $event"
    @update:service-value-pack="serviceValuePack = $event"
    @submit="handleSubmit"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ServiceApprovalTable from '@/components/service/ServiceApprovalTable.vue';

// Sample data - replace with actual data fetching
const termLength = ref("5");
const serviceValuePack = ref("Gold");
const serviceLevels = ref([
  { label: 'Basic', value: 'basic' },
  { label: 'Gold', value: 'gold' },
  { label: 'Platinum', value: 'platinum' },
  { label: 'Diamond', value: 'diamond' }
]);

// Sample table data - replace with actual data fetching
const tableSections = ref([
  {
    section_name: "B&W",
    data_rows: []
  },
  {
    section_name: "Color",
    data_rows: [
      {
        "Model": "iRC5850i",
        "Qty": "3",
        "Estimated AMV / Unit": "10,333",
        "Colour %": "84%",
        "Oversize %": "20%",
        "Inclusive Plan Yes/No": "No",
        "serviceOptions": [
          {
            "termLength": "24 Months",
            "Published": {
              "B&W": "0.01043", "Colour": "0.07731",
              "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
            },
            "Requested": {
              "B&W": "0.00680", "Colour": "0.05000",
              "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
            },
            "Approved": {
              "B&W": "0.00680", "Colour": "0.05297",
              "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
            }
          },
          {
            "termLength": "36 Months",
            "Published": {
              "B&W": "0.01000", "Colour": "0.07500",
              "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
            },
            "Requested": {
              "B&W": "0.00650", "Colour": "0.04800",
              "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
            },
            "Approved": {
              "B&W": "0.00650", "Colour": "0.04800",
              "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
            }
          }
        ]
      },
      {
        "Model": "iRC5860i",
        "Qty": "1",
        "Estimated AMV / Unit": "10,467",
        "Colour %": "84%",
        "Oversize %": "20%",
        "Inclusive Plan Yes/No": "No",
        "serviceOptions": [
          {
            "termLength": "48 Months",
            "Published": {
              "B&W": "0.01006", "Colour": "0.07486",
              "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
            },
            "Requested": {
              "B&W": "0.00650", "Colour": "0.04900",
              "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
            },
            "Approved": {
              "B&W": "0.00650", "Colour": "0.05097",
              "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
            }
          }
        ]
      },
      {
        "Model": "iRC5870i",
        "Qty": "1",
        "Estimated AMV / Unit": "13,804",
        "Colour %": "89%",
        "Oversize %": "20%",
        "Inclusive Plan Yes/No": "No",
        "Published": {
          "B&W": "0.01006", "Colour (LTR)": "0.07486", "Colour (Oversize)": "0.07486",
          "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
        },
        "Requested": {
          "B&W": "0.00650", "Colour (LTR)": "0.04900", "Colour (Oversize)": "0.04900",
          "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
        },
        "Approved": {
          "B&W": "0.00650", "Colour (LTR)": "0.05097", "Colour (Oversize)": "0.05097",
          "Extra Long (IPRC only)": null, "Base $Amt": null, "Base Volume": null
        }
      }
    ]
  }
]);

const handleSubmit = async (approvedData: any) => {
  try {
    console.log('Submitting approval data:', approvedData);
    // Example API call:
    // await apiService.submitApproval(approvedData);
    
    // Show success message
    // useToast().success('Approval submitted successfully');
    alert('Approval submitted successfully!');
  } catch (error) {
    console.error('Error submitting approval:', error);
    // useToast().error('Failed to submit approval');
    alert('Failed to submit approval. Please try again.');
  }
};
</script>

<style scoped>
/* Add any page-specific styles here */
</style>
