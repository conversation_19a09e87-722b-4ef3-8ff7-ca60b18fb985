<script setup lang="ts">
import { defineProps, defineEmits, computed, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      selected_region: null, // Changed from object to single string
      dsd_territory: false,
      dealer_territory: false,
      dealer_accepted_service_rates: null, // 'Yes' or 'No'
      // coterm: null, // Removed
      purchase_lease_type: 'Lease', // Default, now view-only in this component
      length_of_lease_months: 36, // Default, now view-only in this component
      msrp_details: { 
        percentage: 85, // Default 85%
        amount: 42500 // Default $42,500
      }
    }),
  },
});

const emit = defineEmits(['update:modelValue']);

const localData = computed({
  get: () => {
    // Only provide defaults if the values are null/undefined
    const msrpDetails = {
      percentage: props.modelValue.msrp_details?.percentage ?? 85,
      amount: props.modelValue.msrp_details?.amount ?? 42500,
      ...props.modelValue.msrp_details
    };
    
    return {
      ...props.modelValue,
      msrp_details: msrpDetails
    };
  },
  get: () => {
    // Default structure, ensuring all fields are present
    const defaults = {
      selected_region: null,
      dsd_territory: false,
      dealer_territory: false,
      dealer_accepted_service_rates: null,
      purchase_lease_type: 'Lease',
      length_of_lease_months: 36,
      msrp_details: { percentage: 85, amount: 42500 }
    };
    const model = { ...defaults, ...props.modelValue };
    // Ensure nested objects like msrp_details are also properly merged or defaulted
    model.msrp_details = { 
      ...defaults.msrp_details, 
      ...(props.modelValue.msrp_details || {}) 
    };
    return model;
  },
  set: (newState) => {
    emit('update:modelValue', newState);
  }
});

const regionOptions = [
  { label: 'Central', value: 'central' },
  { label: 'Eastern', value: 'eastern' },
  { label: 'Western', value: 'western' },
];

// Validation Rules
const regionRule = [
  (value: string | null) => !!value || 'A region must be selected.',
];

const territoryRule = [
  (v: { dsd_territory: boolean, dealer_territory: boolean }) => (v.dsd_territory || v.dealer_territory) || 'A territory (DSD or Dealer) must be selected.',
];

const dealerAcceptedRatesRule = [
  (value: string | null) => !!value || 'This field is required, Please select an option.',
];

// Computed property to check if dealer territory is selected
const isDealerTerritorySelected = computed(() => localData.value.dealer_territory);

// Watch for changes in dealer_territory to reset dealer_accepted_service_rates
watch(() => localData.value.dealer_territory, (isDealerSelected) => {
  if (!isDealerSelected && localData.value.dealer_accepted_service_rates !== null) {
    // localData.value setter will handle the emit
    localData.value = { ...localData.value, dealer_accepted_service_rates: null };
  }
}, { immediate: false });

const handleRegionChange = (regionValue: string) => {
  const newSelectedRegion = localData.value.selected_region === regionValue ? null : regionValue;
  localData.value = { ...localData.value, selected_region: newSelectedRegion };
};

const handleTerritoryChange = (territoryField: 'dsd_territory' | 'dealer_territory', isChecked: boolean) => {
  let newDsd = localData.value.dsd_territory;
  let newDealer = localData.value.dealer_territory;

  if (territoryField === 'dsd_territory') {
    newDsd = isChecked;
    if (isChecked) newDealer = false; // If DSD is checked, uncheck Dealer
  } else if (territoryField === 'dealer_territory') {
    newDealer = isChecked;
    if (isChecked) newDsd = false; // If Dealer is checked, uncheck DSD
  }
  // Assign a new object to localData.value to trigger the setter and reactivity
  localData.value = { 
    ...localData.value, 
    dsd_territory: newDsd, 
    dealer_territory: newDealer 
  };
};

const handleDealerAcceptedRatesChange = (newValue: 'Yes' | 'No' | null) => {
  localData.value = { 
    ...localData.value, 
    dealer_accepted_service_rates: newValue 
  };
};

</script>

<template>
  <v-card class="mb-4">
    <v-expansion-panels>
      <v-expansion-panel>
        <v-expansion-panel-title>
          <v-row no-gutters>
            <v-col class="text-h6">Approvals and Lease Details</v-col>
          </v-row>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <v-card-text class="pt-4">
            <v-row>
              <!-- Region -->
              <v-col cols="12" md="6">
                <p class="mb-1 font-weight-medium">Region <span style="color:red">*</span></p>
                <v-input :rules="regionRule" :model-value="localData.selected_region" class="pt-0 mt-n3 mb-n4">
                  <v-checkbox
                    v-for="option in regionOptions"
                    :key="option.value"
                    :label="option.label"
                    :model-value="localData.selected_region === option.value"
                    @update:model-value="handleRegionChange(option.value)"
                    density="compact"
                    hide-details
                    class="pb-0 mb-0 d-block"
                  />
                </v-input>
              </v-col>

              <!-- DSD / Dealer Territory -->
              <v-col cols="12" md="6">
                <p class="mb-1 font-weight-medium">Territory <span style="color:red">*</span></p>
                <v-input :rules="territoryRule" :model-value="{ dsd_territory: localData.dsd_territory, dealer_territory: localData.dealer_territory }" class="pt-0 mt-n3 mb-n4">
                  <v-checkbox
                    :model-value="localData.dsd_territory"
                    @update:model-value="(isChecked) => handleTerritoryChange('dsd_territory', !!isChecked)"
                    label="DSD Territory"
                    density="compact"
                    hide-details
                    class="pb-0 mb-0 d-block"
                  />
                  <v-checkbox
                    :model-value="localData.dealer_territory"
                    @update:model-value="(isChecked) => handleTerritoryChange('dealer_territory', !!isChecked)"
                    label="Dealer Territory"
                    density="compact"
                    hide-details
                    class="pb-0 mb-0 d-block"
                  />
                </v-input>
              </v-col>

              <!-- Has dealer accepted Service Rates (Conditional) -->
              <v-col cols="12" md="6" v-if="isDealerTerritorySelected">
                <p class="mb-1 font-weight-medium">Has dealer accepted Service Rates? <span style="color:red">*</span></p>
                <v-radio-group 
                  :model-value="localData.dealer_accepted_service_rates" 
                  @update:model-value="handleDealerAcceptedRatesChange"
                  inline 
                  density="compact" 
                  :rules="dealerAcceptedRatesRule"
                >
                  <v-radio label="Yes" value="Yes"></v-radio>
                  <v-radio label="No" value="No"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col v-else cols="12" md="6"></v-col> <!-- Placeholder for alignment -->

              <!-- Purchase / Lease (becomes Payment Mode - View Only) -->
              <v-col cols="12" md="6">
                <p class="mb-1 font-weight-medium">Payment Mode</p>
                <div class="text-body-1 pt-2">{{ localData.purchase_lease_type || 'N/A' }}</div>
              </v-col>

              <!-- Lease Term (Conditional, View Only) -->
              <v-col cols="12" md="6" v-if="localData.purchase_lease_type === 'Lease'">
                <p class="mb-1 font-weight-medium">Lease Term (months)</p>
                <div class="text-body-1 pt-2">{{ localData.length_of_lease_months !== null ? localData.length_of_lease_months : 'N/A' }}</div>
              </v-col>
              <v-col v-else cols="12" md="6"></v-col> <!-- Placeholder for alignment -->

              <!-- MSRP Information (Read-only) -->
              <v-row class="mt-4">
                <v-col cols="12">
                  <div class="text-subtitle-1 font-weight-medium mb-2">MSRP Information</div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex flex-column">
                    <span class="text-caption text-grey">% of MSRP</span>
                    <span class="text-h6 font-weight-bold">{{ localData.msrp_details?.percentage ?? 0 }}%</span>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex flex-column">
                    <span class="text-caption text-grey">MSRP Value</span>
                    <span class="text-h6 font-weight-bold">${{ (localData.msrp_details?.amount ?? 0).toLocaleString() }}</span>
                  </div>
                </v-col>
              </v-row>
            </v-row>
          </v-card-text>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<style scoped>
/* Component-specific styles */
.font-weight-medium {
  font-weight: 500;
}

.text-caption {
  line-height: 1.2;
  margin-bottom: 2px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.75rem;
  font-weight: 400;
}

.text-body-1 {
  line-height: 1.5;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0.87);
}
</style>
