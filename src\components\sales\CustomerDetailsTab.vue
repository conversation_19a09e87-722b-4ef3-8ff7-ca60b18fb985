<script setup lang="ts">
/**
 * @file Customer Details tab component for Sales Request Form.
 * @version 1.0.0
 * @since 1.0.0
 */

import { ref, computed, reactive, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useUserStore } from '@/stores/UserStore';
import LocationManager from '@/components/sales/LocationManager.vue';
import CustomerManager from '@/components/sales/CustomerManager.vue';

// Add language support
const { t } = useI18n();

// Get user store for logged-in user info
const userStore = useUserStore();

// Add a ref for the date picker menu
const rfpDueDateMenu = ref(false);

// Compute sales rep options based on logged-in user
const salesRepOptions = computed(() => {
    if (userStore.authenticated) {
        return [{
            title: `${userStore.givenName} ${userStore.surName}`,
            value: userStore.microsoftID
        }];
    }
    return [];
});

// Form validation state
const formState = reactive({
    errors: {} as Record<string, string>,
    touched: {} as Record<string, boolean>
});

// Form data with validation rules
const customerDetails = ref({
    businessName: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val.trim() !== '' ? '' : 'Business name is required'
    },
    legalName: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    salesforceOppId: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    customerRelationship: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Customer relationship is required'
    },
    rfp: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'RFP selection is required'
    },
    rfpDueDate: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'RFP due date is required'
    },
    location: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Location is required'
    },
    customerWebsite: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => {
            if (!val) return 'Customer website is required';
            // Basic URL validation
            try {
                new URL(val.startsWith('http') ? val : `https://${val}`);
                return '';
            } catch {
                return 'Invalid website URL';
            }
        }
    },
    globalAgreement: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Global agreement is required'
    },
    branch: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    salesRep: {
        value: userStore.authenticated ? userStore.microsoftID : '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Sales rep is required'
    },
    salesManager: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    regionalLeader: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    salesChannel: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Sales channel is required'
    },
    portfolio: {
        value: '',
        required: true,
        error: '',
        validate: () => ''
    },
    printAssessment: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'Print assessment is required'
    },
    coreItsNetworkScan: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'CORE ITS network scan is required'
    },
    msa: {
        value: '',
        required: true,
        error: '',
        validate: (val: string) => val ? '' : 'MSA is required'
    }
});

// Helper function to generate field labels with conditional asterisks
const getFieldLabel = (fieldKey: string) => {
    const field = customerDetails.value[fieldKey as keyof typeof customerDetails.value];
    const translationKey = `page.sales_request_form.customer_details.${fieldKey}`;
    return t(translationKey) + (field.required ? ' *' : '');
};

// Validate a single field
const validateField = (fieldName: string) => {
    const field = customerDetails.value[fieldName as keyof typeof customerDetails.value];
    field.error = field.validate(field.value || '');
    formState.touched[fieldName] = true;
    return field.error === '';
};

// Validate all fields
const validateForm = () => {
    let isValid = true;
    
    for (const fieldName in customerDetails.value) {
        if (!validateField(fieldName)) {
            isValid = false;
        }
    }
    
    return isValid;
};

const setFormData = (data: any) => {
    if (!data) return;
    // Map backend fields to frontend state
    const mapping: { [key: string]: string } = {
        businessId: 'businessName',
        locationId: 'location',
        salesRepOid: 'salesRep',
        salesManagerOid: 'salesManager',
        regionalLeaderOid: 'regionalLeader',
        portfolioId: 'portfolio'
    };

    for (const backendKey in data) {
        const frontendKey = mapping[backendKey] || backendKey;
        if (customerDetails.value[frontendKey as keyof typeof customerDetails.value]) {
            customerDetails.value[frontendKey as keyof typeof customerDetails.value].value = data[backendKey];
        }
    }
};

// Expose validation methods and form data to parent component
defineExpose({
    validateForm,
    getFormData: () => {
        const details = customerDetails.value;
        // Map frontend state to the backend model.
        // NOTE: This assumes that for fields like businessName and location, the
        // component's `v-model` holds the ID required by the backend.
        return {
            businessId: details.businessName.value,
            legalName: details.legalName.value,
            salesforceOppId: details.salesforceOppId.value,
            customerRelationship: details.customerRelationship.value,
            rfp: details.rfp.value,
            rfpDueDate: details.rfpDueDate.value,
            locationId: details.location.value,
            customerWebsite: details.customerWebsite.value,
            globalAgreement: details.globalAgreement.value,
            branch: details.branch.value,
            salesRepOid: details.salesRep.value,
            salesManagerOid: details.salesManager.value,
            regionalLeaderOid: details.regionalLeader.value,
            salesChannel: details.salesChannel.value,
            portfolioId: details.portfolio.value,
            printAssessment: details.printAssessment.value,
            coreItsNetworkScan: details.coreItsNetworkScan.value,
            msa: details.msa.value,
        };
    },
    setFormData
});

// Sample dropdown options
const customerRelationshipOptions = ['Good', 'Strong', 'Fair','Weak'];
const rfpOptions = ['Yes', 'No'];
const agreementOptions = ['Yes', 'No'];
const channelOptions = ['Direct', 'Partner', 'Reseller'];
const portfolioOptions = ['Commercial', 'Corporate', 'Production-Graphics', 'National'];
const assessmentOptions = ['Yes', 'No'];
</script>

<template>
    <v-container fluid>
        <v-row>
            <!-- Left Column -->
            <v-col cols="12" md="6">
                <v-row>
                    <v-col cols="12">
                        <CustomerManager
                            v-model="customerDetails.businessName.value"
                            :error="customerDetails.businessName.error"
                            @validate="validateField('businessName')"
                        />
                    </v-col>
                    
                    <v-col cols="12">
                        <v-text-field density="compact"
                            v-model="customerDetails.legalName.value"
                            :label="getFieldLabel('legalName')"
                            :error-messages="customerDetails.legalName.error"
                            @blur="validateField('legalName')"
                        ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-text-field density="compact"
                            v-model="customerDetails.salesforceOppId.value"
                            :label="getFieldLabel('salesforceOppId')"
                            :error-messages="customerDetails.salesforceOppId.error"
                            @blur="validateField('salesforceOppId')"
                        ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
                            v-model="customerDetails.customerRelationship.value"
                            :items="customerRelationshipOptions"
                            :label="getFieldLabel('customerRelationship')"
                            :error-messages="customerDetails.customerRelationship.error"
                            @blur="validateField('customerRelationship')"
                        ></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
                            v-model="customerDetails.rfp.value"
                            :items="rfpOptions"
                            :label="getFieldLabel('rfp')"
                            :error-messages="customerDetails.rfp.error"
                            @blur="validateField('rfp')"
                        ></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-menu
                            v-model="rfpDueDateMenu"
                            :close-on-content-click="false"
                            transition="scale-transition"
                            offset-y
                            min-width="auto"
                        >
                            <template v-slot:activator="{ props }">
                                <v-text-field
                                    density="compact"
                                    v-model="customerDetails.rfpDueDate.value"
                                    :label="getFieldLabel('rfpDueDate')"
                                    :error-messages="customerDetails.rfpDueDate.error"
                                    type="date"
                                    v-bind="props"
                                    @blur="validateField('rfpDueDate')"
                                ></v-text-field>
                            </template>
                        </v-menu>
                    </v-col>
                    
            
                </v-row>
            </v-col>
            
            <!-- Right Column -->
            <v-col cols="12" md="6">
                <v-row>
                    <v-col cols="12">
                        <LocationManager
                            v-model="customerDetails.location.value"
                            :error="customerDetails.location.error"
                            @validate="validateField('location')"
                        />
                    </v-col>
                    
                    <v-col cols="12">
                        <v-text-field density="compact"
                            v-model="customerDetails.customerWebsite.value"
                            :label="getFieldLabel('customerWebsite')"
                            :error-messages="customerDetails.customerWebsite.error"
                            @blur="validateField('customerWebsite')"
                        ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
                            v-model="customerDetails.globalAgreement.value"
                            :items="agreementOptions"
                            :label="getFieldLabel('globalAgreement')"
                            :error-messages="customerDetails.globalAgreement.error"
                            @blur="validateField('globalAgreement')"
                        ></v-select>
                    </v-col>
                    
                 
                    
                    <v-col cols="12">
                        <v-select density="compact"
                            v-model="customerDetails.coreItsNetworkScan.value"
                            :items="assessmentOptions"
                            :label="getFieldLabel('coreItsNetworkScan')"
                            :error-messages="customerDetails.coreItsNetworkScan.error"
                            @blur="validateField('coreItsNetworkScan')"
                        ></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
                            v-model="customerDetails.msa.value"
                            :items="assessmentOptions"
                            :label="getFieldLabel('msa')"
                            :error-messages="customerDetails.msa.error"
                            @blur="validateField('msa')"
                        ></v-select>
                    </v-col>
                    
                    <v-col cols="12">
                        <v-select density="compact"
                            v-model="customerDetails.printAssessment.value"
                            :items="assessmentOptions"
                            :label="getFieldLabel('printAssessment')"
                            :error-messages="customerDetails.printAssessment.error"
                            @blur="validateField('printAssessment')"
                        ></v-select>
                    </v-col>
                </v-row>
            </v-col>
        </v-row>
        
        <!-- Sales Rep Info Section -->
        <v-row class="mt-4">
            <v-col cols="12">
                <v-card>
                    <v-card-title class="text-h6 bg-grey-lighten-3">
                        Sales Rep Info
                    </v-card-title>
                    <v-card-text>
                        <v-row>
                            <v-col cols="12" md="4">
                                <v-select density="compact"
                                    v-model="customerDetails.salesRep.value"
                                    :items="salesRepOptions"
                                    item-title="title"
                                    item-value="value"
                                    :label="getFieldLabel('salesRep')"
                                    readonly
                                ></v-select>
                            </v-col>
                            
                            <v-col cols="12" md="4">
                                <v-select density="compact"
                                    v-model="customerDetails.salesManager.value"
                                    :items="salesRepOptions"
                                    item-title="title"
                                    item-value="value"
                                    :error-messages="customerDetails.salesManager.error"
                                    @blur="validateField('salesManager')"
                                    :label="getFieldLabel('salesManager')"
                                    readonly
                                ></v-select>
                            </v-col>
                            
                            <v-col cols="12" md="4">
                                <v-select density="compact"
                                    v-model="customerDetails.regionalLeader.value"
                                    :items="salesRepOptions"
                                    item-title="title"
                                    item-value="value"
                                    :error-messages="customerDetails.regionalLeader.error"
                                    @blur="validateField('regionalLeader')"
                                    :label="getFieldLabel('regionalLeader')"
                                    readonly
                                ></v-select>
                            </v-col>
                                 <v-col cols="12"  md="4">
                        <v-select density="compact"
                            v-model="customerDetails.salesChannel.value"
                            :items="channelOptions"
                            :label="getFieldLabel('salesChannel')"
                            :error-messages="customerDetails.salesChannel.error"
                            @blur="validateField('salesChannel')"
                        ></v-select>
                    </v-col>
                    
                    <v-col cols="12"  md="4">
                        <v-select density="compact"
                            v-model="customerDetails.portfolio.value"
                            :items="portfolioOptions"
                            :label="getFieldLabel('portfolio')"
                            :error-messages="customerDetails.portfolio.error"
                            @blur="validateField('portfolio')"
                        ></v-select>
                    </v-col>
                    <v-col cols="12" md="4">
                        <v-text-field density="compact"
                            v-model="customerDetails.branch.value"
                            :label="getFieldLabel('branch')"
                            :error-messages="customerDetails.branch.error"
                            @blur="validateField('branch')"
                        ></v-text-field>
                    </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
























